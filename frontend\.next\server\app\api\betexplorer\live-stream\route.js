/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/betexplorer/live-stream/route";
exports.ids = ["app/api/betexplorer/live-stream/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-stream%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-stream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-stream%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-stream%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-stream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-stream%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_Administrator_Documents_3_frontend_src_app_api_betexplorer_live_stream_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/betexplorer/live-stream/route.ts */ \"(rsc)/./src/app/api/betexplorer/live-stream/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/betexplorer/live-stream/route\",\n        pathname: \"/api/betexplorer/live-stream\",\n        filename: \"route\",\n        bundlePath: \"app/api/betexplorer/live-stream/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\3\\\\frontend\\\\src\\\\app\\\\api\\\\betexplorer\\\\live-stream\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Administrator_Documents_3_frontend_src_app_api_betexplorer_live_stream_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/betexplorer/live-stream/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-stream%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-stream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-stream%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/betexplorer/live-stream/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/betexplorer/live-stream/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/betexplorer-types */ \"(rsc)/./src/lib/betexplorer-types.ts\");\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n\n// Cache para detectar mudanças\nlet lastDataHash = '';\nlet cachedData = null;\nasync function GET(request) {\n    console.log('🔄 Iniciando stream SSE para BetExplorer (todos os jogos)...');\n    // Configurar headers SSE\n    const headers = new Headers({\n        'Content-Type': 'text/event-stream; charset=utf-8',\n        'Cache-Control': 'no-cache, no-store, must-revalidate',\n        'Connection': 'keep-alive',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Headers': 'Cache-Control, Content-Type',\n        'Access-Control-Allow-Methods': 'GET, OPTIONS',\n        'X-Accel-Buffering': 'no'\n    });\n    // Criar stream\n    const stream = new ReadableStream({\n        start (controller) {\n            // Função para enviar dados (usando evento padrão 'message')\n            const sendData = (data, eventType = 'data')=>{\n                try {\n                    // Usar formato padrão SSE sem evento customizado\n                    const message = `data: ${JSON.stringify({\n                        type: eventType,\n                        ...data\n                    })}\\n\\n`;\n                    console.log(`📤 Enviando dados ${eventType}:`, data.matches.length, 'jogos');\n                    controller.enqueue(new TextEncoder().encode(message));\n                } catch  {\n                    console.log('⚠️ Controller já fechado, ignorando envio');\n                }\n            };\n            // Função para buscar e comparar dados\n            const fetchAndCompare = async ()=>{\n                try {\n                    console.log('🔍 Iniciando busca de dados...');\n                    const newData = await fetchLiveData();\n                    const newDataHash = JSON.stringify(newData);\n                    console.log('📊 Dados obtidos:', {\n                        matches: newData.matches.length,\n                        hash: newDataHash.substring(0, 50) + '...'\n                    });\n                    // Só enviar se houve mudanças\n                    if (newDataHash !== lastDataHash) {\n                        console.log('📡 Enviando dados atualizados via SSE');\n                        sendData(newData);\n                        lastDataHash = newDataHash;\n                        cachedData = newData;\n                    } else {\n                        console.log('⏸️ Dados inalterados, não enviando');\n                    }\n                } catch (error) {\n                    console.error('❌ Erro ao buscar dados:', error);\n                    // Usar dados do cache se disponível em caso de erro\n                    if (cachedData) {\n                        console.log('⚠️ Usando dados do cache devido ao erro');\n                        sendData(cachedData, 'cached');\n                    } else {\n                        // Enviar dados de fallback se não houver cache\n                        const fallbackData = {\n                            matches: [],\n                            totalMatches: 0,\n                            lastUpdated: new Date().toISOString(),\n                            footballOnly: true\n                        };\n                        sendData(fallbackData, 'fallback');\n                    }\n                }\n            };\n            // Enviar dados de teste imediatamente\n            console.log('🚀 Enviando dados de teste...');\n            const testData = {\n                matches: [],\n                totalMatches: 0,\n                lastUpdated: new Date().toISOString(),\n                footballOnly: true\n            };\n            sendData(testData, 'initial');\n            // Enviar dados iniciais se disponíveis\n            if (cachedData) {\n                console.log('📦 Enviando dados do cache inicialmente');\n                sendData(cachedData, 'initial');\n            }\n            // Buscar dados imediatamente\n            fetchAndCompare();\n            // Configurar intervalo para monitorar mudanças (30 segundos)\n            const interval = setInterval(fetchAndCompare, 5000);\n            // Cleanup quando conexão for fechada\n            request.signal.addEventListener('abort', ()=>{\n                console.log('🛑 Conexão SSE fechada');\n                clearInterval(interval);\n                controller.close();\n            });\n            // Heartbeat para manter conexão viva\n            const heartbeat = setInterval(()=>{\n                const message = `event: heartbeat\\ndata: ${JSON.stringify({\n                    timestamp: new Date().toISOString()\n                })}\\n\\n`;\n                controller.enqueue(new TextEncoder().encode(message));\n            }, 15000) // A cada 15 segundos\n            ;\n            request.signal.addEventListener('abort', ()=>{\n                clearInterval(heartbeat);\n            });\n        }\n    });\n    return new Response(stream, {\n        headers\n    });\n}\n// Função para buscar dados do BetExplorer (todos os jogos disponíveis)\nasync function fetchLiveData() {\n    // 1. Buscar live results\n    const liveResultsUrl = `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_0__.BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/live-results.php`;\n    const liveResponse = await fetch(liveResultsUrl, {\n        method: 'GET',\n        headers: _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_0__.BETEXPLORER_CONFIG.HEADERS,\n        signal: AbortSignal.timeout(10000)\n    });\n    if (!liveResponse.ok) {\n        throw new Error(`Live Results API retornou status ${liveResponse.status}`);\n    }\n    const liveData = await liveResponse.json();\n    if (!liveData || !liveData.events) {\n        throw new Error('Dados de live results inválidos');\n    }\n    // 2. Filtrar apenas eventos de futebol (sem limite)\n    const footballEvents = Object.entries(liveData.events).filter(([, event])=>event.sport_id === _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_0__.SPORT_IDS.FOOTBALL);\n    console.log(`⚽ Encontrados ${footballEvents.length} eventos de futebol`);\n    // 3. Processar eventos com detalhes básicos (otimizado para velocidade)\n    const matches = [];\n    for(let i = 0; i < footballEvents.length; i++){\n        const [eventId, eventData] = footballEvents[i];\n        const event = eventData;\n        // Buscar informações do estágio\n        const stageInfo = liveData.event_stages?.[event.event_stage_id];\n        const stageName = stageInfo ? stageInfo[0] : 'Competição';\n        // Tentar buscar detalhes completos incluindo logos\n        let homeTeam = 'Time Casa';\n        let awayTeam = 'Time Visitante';\n        let homeTeamLogo;\n        let awayTeamLogo;\n        try {\n            const details = await fetchMatchDetailsQuick(eventId);\n            if (details) {\n                homeTeam = details.homeTeam;\n                awayTeam = details.awayTeam;\n                homeTeamLogo = details.homeTeamLogo;\n                awayTeamLogo = details.awayTeamLogo;\n                console.log(`🏆 Detalhes obtidos para ${eventId}:`, {\n                    homeTeam,\n                    awayTeam,\n                    score: event.score,\n                    minute: event.minute,\n                    finished: event.finished,\n                    hasHomeLogo: !!homeTeamLogo,\n                    hasAwayLogo: !!awayTeamLogo\n                });\n            }\n        } catch  {\n            console.log(`⚠️ Não foi possível buscar detalhes para ${eventId}, usando fallback`);\n        }\n        const match = {\n            event_id: eventId,\n            homeTeam,\n            awayTeam,\n            homeTeamLogo,\n            awayTeamLogo,\n            score: event.score || '0:0',\n            minute: parseInt(event.minute) || 0,\n            finished: event.finished === 1,\n            competition: stageName,\n            country: '',\n            isLive: event.finished === 0 && parseInt(event.minute) > 0,\n            odds: {\n                live_odds_1x2: event.live_odds_1x2,\n                live_odds_ou: event.live_odds_ou,\n                live_odds_dc: event.live_odds_dc,\n                live_odds_dnb: event.live_odds_dnb,\n                live_odds_btts: event.live_odds_btts,\n                live_odds_ah: event.live_odds_ah\n            }\n        };\n        matches.push(match);\n        // Rate limiting entre requisições\n        if (i < footballEvents.length - 1) {\n            await new Promise((resolve)=>setTimeout(resolve, 200));\n        }\n    }\n    return {\n        matches,\n        totalMatches: matches.length,\n        lastUpdated: new Date().toISOString(),\n        footballOnly: true\n    };\n}\n// Função otimizada para buscar detalhes completos incluindo logos\nasync function fetchMatchDetailsQuick(eventId) {\n    try {\n        const apiUrl = `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_0__.BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/match-content.php?e=${eventId}&t=new&bt=1x2&lang=en`;\n        const response = await fetch(apiUrl, {\n            method: 'GET',\n            headers: _lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_0__.BETEXPLORER_CONFIG.HEADERS,\n            signal: AbortSignal.timeout(5000) // Timeout um pouco maior para buscar logos\n        });\n        if (!response.ok) {\n            return null;\n        }\n        const htmlContent = await response.text();\n        if (!htmlContent) {\n            return null;\n        }\n        // Parse do HTML para extrair nomes dos times e logos\n        const $ = cheerio__WEBPACK_IMPORTED_MODULE_1__.load(htmlContent);\n        const teamNames = [];\n        const teamLogos = [];\n        // Buscar nomes dos times\n        $('h3').each((index, element)=>{\n            if (index < 2) {\n                const teamName = $(element).text().trim();\n                if (teamName) {\n                    teamNames.push(teamName);\n                }\n            }\n        });\n        // Buscar logos dos times\n        $('.team-logo img, .logo img, img[src*=\"team\"], img[src*=\"logo\"]').each((index, element)=>{\n            if (index < 2) {\n                const logoSrc = $(element).attr('src');\n                if (logoSrc) {\n                    // Converter URL relativa para absoluta se necessário\n                    const fullLogoUrl = logoSrc.startsWith('http') ? logoSrc : `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_0__.BETEXPLORER_CONFIG.BASE_URL}${logoSrc.startsWith('/') ? '' : '/'}${logoSrc}`;\n                    teamLogos.push(fullLogoUrl);\n                }\n            }\n        });\n        // Buscar logos alternativos se não encontrou\n        if (teamLogos.length === 0) {\n            $('img').each((_, element)=>{\n                const src = $(element).attr('src');\n                const alt = $(element).attr('alt');\n                if (src && (src.includes('team') || src.includes('logo') || src.includes('club') || alt && (alt.includes('logo') || alt.includes('team')))) {\n                    const fullLogoUrl = src.startsWith('http') ? src : `${_lib_betexplorer_types__WEBPACK_IMPORTED_MODULE_0__.BETEXPLORER_CONFIG.BASE_URL}${src.startsWith('/') ? '' : '/'}${src}`;\n                    teamLogos.push(fullLogoUrl);\n                    if (teamLogos.length >= 2) return false // Parar após encontrar 2\n                    ;\n                }\n            });\n        }\n        if (teamNames.length >= 2) {\n            return {\n                homeTeam: teamNames[0],\n                awayTeam: teamNames[1],\n                homeTeamLogo: teamLogos[0] || undefined,\n                awayTeamLogo: teamLogos[1] || undefined\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(`❌ Erro ao buscar detalhes do evento ${eventId}:`, error);\n        return null;\n    }\n}\nconst runtime = 'nodejs';\nconst dynamic = 'force-dynamic';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/betexplorer/live-stream/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/betexplorer-types.ts":
/*!**************************************!*\
  !*** ./src/lib/betexplorer-types.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BETEXPLORER_CONFIG: () => (/* binding */ BETEXPLORER_CONFIG),\n/* harmony export */   MARKET_TYPES: () => (/* binding */ MARKET_TYPES),\n/* harmony export */   SPORT_IDS: () => (/* binding */ SPORT_IDS)\n/* harmony export */ });\n/**\n * Tipos TypeScript para as APIs do BetExplorer\n * Baseado na documentação completa das APIs descobertas\n */ // ============================================================================\n// TIPOS BASE\n// ============================================================================\n// ============================================================================\n// CONFIGURAÇÕES E CONSTANTES\n// ============================================================================\nconst BETEXPLORER_CONFIG = {\n    BASE_URL: 'https://www.betexplorer.com',\n    RATE_LIMIT_MS: 2000,\n    CACHE_TTL_MS: 30000,\n    HEADERS: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n        'Accept': 'application/json, text/html, */*',\n        'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',\n        'X-Requested-With': 'XMLHttpRequest',\n        'Referer': 'https://www.betexplorer.com'\n    }\n};\nconst SPORT_IDS = {\n    FOOTBALL: 1,\n    TENNIS: 2,\n    BASKETBALL: 3,\n    HOCKEY: 4\n};\nconst MARKET_TYPES = {\n    ODDS_1X2: '1x2',\n    OVER_UNDER: 'ou',\n    ASIAN_HANDICAP: 'ah',\n    DRAW_NO_BET: 'dnb',\n    DOUBLE_CHANCE: 'dc',\n    BOTH_TEAMS_TO_SCORE: 'btts'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/betexplorer-types.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:sqlite");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/undici","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/css-select","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/htmlparser2","vendor-chunks/whatwg-mimetype","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/safer-buffer","vendor-chunks/boolbase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbetexplorer%2Flive-stream%2Froute&page=%2Fapi%2Fbetexplorer%2Flive-stream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbetexplorer%2Flive-stream%2Froute.ts&appDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDocuments%5C3%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();