/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configurações básicas para compatibilidade com Vercel
  experimental: {
    ppr: false,
  },
  // Configurações de transpilação para Three.js
  transpilePackages: ['three', '@react-three/fiber', '@react-three/drei'],
  // Configurações de imagens
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
}

module.exports = nextConfig
