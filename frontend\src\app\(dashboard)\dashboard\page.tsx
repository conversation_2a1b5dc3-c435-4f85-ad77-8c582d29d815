'use client'

import { useState, useEffect } from 'react'
import PositionsTable from "@/components/positions-table"
import Calculator from "@/components/calculator"
import PositionsHeader from "@/components/positions-header"

import { RefreshCw, Wifi, WifiOff, AlertCircle } from "lucide-react"
import { DashboardMatch } from "@/lib/betexplorer-types"

export default function Dashboard() {
  console.log('🎯 Dashboard iniciando...')

  // Teste simples com useState e useEffect
  const [matches, setMatches] = useState<DashboardMatch[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<string>('connecting')

  useEffect(() => {
    let retryCount = 0
    const maxRetries = 3
    const retryDelay = 5000 // 5 segundos

    const connectSSE = () => {
      console.log('🔌 Conectando ao SSE diretamente...', retryCount > 0 ? `(tentativa ${retryCount + 1})` : '')

      const eventSource = new EventSource('/api/betexplorer/live-stream')

      eventSource.onopen = () => {
        console.log('✅ Conexão SSE estabelecida')
        setConnectionStatus('connected')
        retryCount = 0 // Reset retry count on successful connection
      }

      eventSource.onmessage = (event) => {
        console.log('📨 Mensagem recebida:', event.data)
        try {
          const data = JSON.parse(event.data)
          console.log('📊 Dados processados:', data)

          if (data.matches) {
            console.log('🏆 Jogos recebidos:', data.matches.map((m: DashboardMatch) => ({
              id: m.event_id,
              homeTeam: m.homeTeam,
              awayTeam: m.awayTeam,
              hasHomeLogo: !!m.homeTeamLogo,
              hasAwayLogo: !!m.awayTeamLogo,
              homeLogoUrl: m.homeTeamLogo,
              awayLogoUrl: m.awayTeamLogo
            })))

            setMatches(data.matches)
            setLoading(false)
            setError(null)
          }
        } catch (err) {
          console.error('❌ Erro ao processar:', err)
        }
      }

      eventSource.onerror = (event) => {
        console.error('❌ Erro SSE:', event)
        console.log('📊 Estado da conexão SSE:', eventSource.readyState)
        setConnectionStatus('error')
        eventSource.close()

        // Retry logic
        if (retryCount < maxRetries) {
          retryCount++
          console.log(`🔄 Tentando reconectar em ${retryDelay/1000}s... (${retryCount}/${maxRetries})`)
          setTimeout(connectSSE, retryDelay)
        } else {
          console.log('❌ Máximo de tentativas atingido')
          setError('Falha na conexão após múltiplas tentativas')
        }
      }

      return eventSource
    }

    const eventSource = connectSSE()

    return () => {
      console.log('🛑 Fechando SSE')
      eventSource.close()
    }
  }, [])

  const reconnect = () => {
    console.log('🔄 Reconectando manualmente...')
    setConnectionStatus('connecting')
    setError(null)
    window.location.reload()
  }

  console.log('🎯 Dashboard renderizado:', {
    matchesCount: matches.length,
    loading,
    error,
    connectionStatus
  })

  // Usar todos os jogos recebidos (sem limite)
  const dashboardMatches = matches.length > 0 ? matches : []

  console.log('📊 Total de jogos para exibir:', dashboardMatches.length)

  // Ícone de status da conexão
  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="h-3 w-3 text-green-500" />
      case 'connecting':
        return <RefreshCw className="h-3 w-3 text-yellow-500 animate-spin" />
      case 'disconnected':
      case 'error':
        return <WifiOff className="h-3 w-3 text-red-500" />
      default:
        return <AlertCircle className="h-3 w-3 text-gray-500" />
    }
  }
  return (
    <div className="h-full grid grid-cols-[2fr_1fr] gap-4">
      {/* Coluna esquerda - Múltiplos Jogos */}
      <div className="flex flex-col h-full gap-1 overflow-y-auto custom-scrollbar">
        {/* Header único para todas as posições */}
        <div className="flex items-center justify-between mb-2">
          <PositionsHeader />
          <div className="flex items-center gap-2">
            {/* Status da conexão */}
            <div className="flex items-center gap-1">
              {getConnectionIcon()}
              <span className="text-xs text-muted-foreground capitalize">
                {connectionStatus}
              </span>
            </div>

            
           
          </div>
        </div>

        {/* Renderizar todos os jogos dinamicamente */}
        {dashboardMatches.length > 0 ? (
          dashboardMatches.map((match, index) => (
            <div key={match?.event_id || `fallback-${index}`} className="bg-card">
              <PositionsTable
                matchData={match}
                loading={loading}
                error={error}
                onRefresh={reconnect}
              />
            </div>
          ))
        ) : (
          // Fallback quando não há dados
          <>
            <div className="bg-card">
              <PositionsTable
                loading={loading}
                error={error}
                onRefresh={reconnect}
                gameTitle="Real Madrid x Inter de Milão"
                league="Champions League"
                time="Hoje, 20:00"
              />
            </div>
            <div className="bg-card">
              <PositionsTable
                loading={loading}
                error={error}
                onRefresh={reconnect}
                gameTitle="Barcelona x PSG"
                league="Champions League"
                time="Amanhã, 16:45"
              />
            </div>
            <div className="bg-card">
              <PositionsTable
                loading={loading}
                error={error}
                onRefresh={reconnect}
                gameTitle="Manchester City x Arsenal"
                league="Premier League"
                time="Sábado, 14:30"
              />
            </div>
            <div className="bg-card">
              <PositionsTable
                loading={loading}
                error={error}
                onRefresh={reconnect}
                gameTitle="Bayern Munich x Borussia Dortmund"
                league="Bundesliga"
                time="Domingo, 17:00"
              />
            </div>
          </>
        )}
      </div>

      {/* Coluna direita - Calculadora */}
      <div>
        <Calculator />
      </div>
    </div>
  );
}
