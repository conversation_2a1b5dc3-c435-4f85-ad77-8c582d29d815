'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '@/hooks/useAuth'
import { useProfile } from '@/hooks/useProfile'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AvatarUpload from '@/components/ui/avatar-upload'

import { User, Mail, Calendar, Save, X, Trash2 } from 'lucide-react'

export default function ProfilePage() {
    const { isLoading: authLoading } = useAuth()
    const {
        profile,
        isLoading: profileLoading,
        error,
        updateProfile,
        uploadAvatar,
        removeAvatar,
        clearError
    } = useProfile()

    const [isEditing, setIsEditing] = useState(false)
    const [isSaving, setIsSaving] = useState(false)
    const [isUploadingAvatar, setIsUploadingAvatar] = useState(false)
    const [fullName, setFullName] = useState('')
    const [success, setSuccess] = useState<string | null>(null)

    // Inicializar campos quando o perfil carregar
    useEffect(() => {
        if (profile) {
            setFullName(profile.full_name || '')
        }
    }, [profile])

    const handleAvatarUpload = async (file: File) => {
        setIsUploadingAvatar(true)
        clearError()

        try {
            const result = await uploadAvatar(file)
            if (result.success) {
                setSuccess('Avatar atualizado com sucesso!')
                setTimeout(() => setSuccess(null), 3000)
            }
        } catch (error: unknown) {
            console.error('Erro no upload:', error)
        } finally {
            setIsUploadingAvatar(false)
        }
    }

    const handleRemoveAvatar = async () => {
        if (!confirm('Tem certeza que deseja remover seu avatar?')) return

        try {
            const result = await removeAvatar()
            if (result?.success) {
                setSuccess('Avatar removido com sucesso!')
                setTimeout(() => setSuccess(null), 3000)
            }
        } catch (error: unknown) {
            console.error('Erro ao remover avatar:', error)
        }
    }

    const handleSaveProfile = async () => {
        if (!profile) return

        setIsSaving(true)
        clearError()

        try {
            const result = await updateProfile({
                full_name: fullName.trim()
            })

            if (result.success) {
                setIsEditing(false)
                setSuccess('Perfil atualizado com sucesso!')
                setTimeout(() => setSuccess(null), 3000)
            }
        } catch (error: unknown) {
            console.error('Erro ao salvar perfil:', error)
        } finally {
            setIsSaving(false)
        }
    }

    const handleCancelEdit = () => {
        setIsEditing(false)
        setFullName(profile?.full_name || '')
        clearError()
    }

    const handleStartEdit = () => {
        setIsEditing(true)
        setFullName(profile?.full_name || '')
    }

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('pt-BR', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })
    }

    if (authLoading || profileLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-background">
                <div className="flex flex-col items-center space-y-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <p className="text-muted-foreground">Carregando perfil...</p>
                </div>
            </div>
        )
    }

    return (
        <ProtectedRoute>
            <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4 py-8 max-w-4xl">
                    {/* Header */}
                    <motion.div
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mb-8"
                    >
                        <h1 className="text-3xl font-bold text-foreground mb-2">Meu Perfil</h1>
                        <p className="text-muted-foreground">
                            Gerencie suas informações pessoais e configurações da conta
                        </p>
                    </motion.div>

                    {/* Mensagens de erro e sucesso */}
                    <AnimatePresence>
                        {error && (
                            <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive"
                            >
                                {error}
                            </motion.div>
                        )}

                        {success && (
                            <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                className="mb-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-green-600"
                            >
                                {success}
                            </motion.div>
                        )}
                    </AnimatePresence>

                    {/* Card do Perfil */}
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-card border border-border rounded-xl shadow-lg overflow-hidden"
                    >
                        {/* Header do Card */}
                        <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-6 border-b border-border">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                    <AvatarUpload
                                        currentAvatar={profile?.avatar_url}
                                        onAvatarChange={handleAvatarUpload}
                                        isUploading={isUploadingAvatar}
                                        disabled={!isEditing}
                                        size="lg"
                                    />

                                    <div>
                                        <h2 className="text-xl font-semibold text-foreground">
                                            {profile?.full_name || 'Usuário'}
                                        </h2>
                                        <p className="text-muted-foreground">{profile?.email}</p>
                                    </div>
                                </div>

                                <div className="flex space-x-2">
                                    {!isEditing ? (
                                        <button
                                            onClick={handleStartEdit}
                                            className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center space-x-2"
                                        >
                                            <User className="w-4 h-4" />
                                            <span>Editar Perfil</span>
                                        </button>
                                    ) : (
                                        <div className="flex space-x-2">
                                            {profile?.avatar_url && (
                                                <button
                                                    onClick={handleRemoveAvatar}
                                                    className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                    <span>Remover Avatar</span>
                                                </button>
                                            )}
                                            <button
                                                onClick={handleCancelEdit}
                                                className="px-4 py-2 bg-muted text-muted-foreground rounded-lg hover:bg-muted/80 transition-colors flex items-center space-x-2"
                                            >
                                                <X className="w-4 h-4" />
                                                <span>Cancelar</span>
                                            </button>
                                            <button
                                                onClick={handleSaveProfile}
                                                disabled={isSaving}
                                                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
                                            >
                                                {isSaving ? (
                                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                ) : (
                                                    <Save className="w-4 h-4" />
                                                )}
                                                <span>{isSaving ? 'Salvando...' : 'Salvar'}</span>
                                            </button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Conteúdo do Card */}
                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Nome Completo */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-foreground flex items-center space-x-2">
                                        <User className="w-4 h-4" />
                                        <span>Nome Completo</span>
                                    </label>
                                    {isEditing ? (
                                        <input
                                            type="text"
                                            value={fullName}
                                            onChange={(e) => setFullName(e.target.value)}
                                            placeholder="Digite seu nome completo"
                                            className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                                        />
                                    ) : (
                                        <p className="px-3 py-2 bg-muted rounded-lg text-foreground">
                                            {profile?.full_name || 'Não informado'}
                                        </p>
                                    )}
                                </div>

                                {/* Email */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-foreground flex items-center space-x-2">
                                        <Mail className="w-4 h-4" />
                                        <span>Email</span>
                                    </label>
                                    <p className="px-3 py-2 bg-muted rounded-lg text-muted-foreground">
                                        {profile?.email}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                        O email não pode ser alterado
                                    </p>
                                </div>

                                {/* Data de Criação */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-foreground flex items-center space-x-2">
                                        <Calendar className="w-4 h-4" />
                                        <span>Membro desde</span>
                                    </label>
                                    <p className="px-3 py-2 bg-muted rounded-lg text-foreground">
                                        {profile?.created_at ? formatDate(profile.created_at) : 'Não disponível'}
                                    </p>
                                </div>

                                {/* Última Atualização */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium text-foreground flex items-center space-x-2">
                                        <Calendar className="w-4 h-4" />
                                        <span>Última Atualização</span>
                                    </label>
                                    <p className="px-3 py-2 bg-muted rounded-lg text-foreground">
                                        {profile?.updated_at ? formatDate(profile.updated_at) : 'Nunca atualizado'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </motion.div>

                
                </div>
            </div>
        </ProtectedRoute>
    )
}