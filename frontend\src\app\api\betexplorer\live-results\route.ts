import { NextRequest, NextResponse } from 'next/server'
import { 
  LiveResultsResponse, 
  BetExplorerResponse, 
  BETEXPLORER_CONFIG,
  SPORT_IDS 
} from '@/lib/betexplorer-types'

/**
 * API Route para Live Results do BetExplorer
 * Funciona como proxy para contornar CORS e adicionar headers necessários
 */

// Cache simples em memória (em produção, usar Redis ou similar)
let cache: {
  data: LiveResultsResponse | null
  timestamp: number
} = {
  data: null,
  timestamp: 0
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId') // Filtro opcional por evento específico
    
    // Verificar cache (30 segundos para dados ao vivo)
    const now = Date.now()
    const cacheValid = cache.data && (now - cache.timestamp) < BETEXPLORER_CONFIG.CACHE_TTL_MS
    
    if (cacheValid && !eventId) {
      console.log('📦 Retornando dados do cache - Live Results')
      return NextResponse.json({
        success: true,
        data: cache.data,
        timestamp: new Date().toISOString(),
        cached: true
      } as BetExplorerResponse<LiveResultsResponse>)
    }

    console.log('🔄 Buscando dados do BetExplorer - Live Results')
    
    // Construir URL da API
    let apiUrl = `${BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/live-results.php`
    if (eventId) {
      apiUrl += `?GETeventId=${eventId}`
    }

    // Fazer requisição para BetExplorer
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: BETEXPLORER_CONFIG.HEADERS,
      // Adicionar timeout
      signal: AbortSignal.timeout(10000) // 10 segundos
    })

    if (!response.ok) {
      throw new Error(`BetExplorer API retornou status ${response.status}`)
    }

    const rawData = await response.json()
    
    // Validar estrutura básica da resposta
    if (!rawData || typeof rawData !== 'object') {
      throw new Error('Resposta inválida da API do BetExplorer')
    }

    // Processar e filtrar dados
    const processedData: LiveResultsResponse = {
      events: {},
      event_stages: rawData.event_stages || {},
      odds: {
        live_odds_1x2: rawData.live_odds_1x2 || {},
        live_odds_ou: rawData.live_odds_ou || {},
        live_odds_dc: rawData.live_odds_dc || {},
        live_odds_dnb: rawData.live_odds_dnb || {},
        live_odds_btts: rawData.live_odds_btts || {},
        live_odds_ah: rawData.live_odds_ah || {}
      },
      bookmakers: rawData.bookmakers || []
    }

    // Filtrar apenas eventos de futebol (sport_id = 1)
    if (rawData.events && typeof rawData.events === 'object') {
      Object.entries(rawData.events).forEach(([eventId, event]: [string, unknown]) => {
        if (event && event.sport_id === SPORT_IDS.FOOTBALL) {
          processedData.events[eventId] = {
            event_id: eventId,
            score: event.score || '0:0',
            minute: parseInt(event.minute) || 0,
            finished: event.finished === 1 ? 1 : 0,
            sport_id: event.sport_id,
            event_stage_id: event.event_stage_id || ''
          }
        }
      })
    }

    // Atualizar cache apenas se não foi filtro específico
    if (!eventId) {
      cache = {
        data: processedData,
        timestamp: now
      }
    }

    console.log(`✅ Dados processados: ${Object.keys(processedData.events).length} eventos de futebol`)

    return NextResponse.json({
      success: true,
      data: processedData,
      timestamp: new Date().toISOString(),
      cached: false,
      eventsCount: Object.keys(processedData.events).length
    } as BetExplorerResponse<LiveResultsResponse>)

  } catch (error) {
    console.error('❌ Erro na API Live Results:', error)
    
    // Se houver dados em cache, retornar mesmo com erro
    if (cache.data) {
      console.log('⚠️ Retornando dados do cache devido ao erro')
      return NextResponse.json({
        success: true,
        data: cache.data,
        timestamp: new Date().toISOString(),
        cached: true,
        warning: 'Dados do cache devido a erro na API'
      } as BetExplorerResponse<LiveResultsResponse>)
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    } as BetExplorerResponse, { status: 500 })
  }
}

// Configurar CORS se necessário
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

// Metadados da rota
export const runtime = 'nodejs'
export const dynamic = 'force-dynamic' // Sempre executar dinamicamente
export const revalidate = 0 // Não cachear no Next.js
