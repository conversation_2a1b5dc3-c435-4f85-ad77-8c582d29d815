import { NextRequest } from 'next/server'
import { 
  DashboardMatch,
  BETEXPLORER_CONFIG,
  SPORT_IDS 
} from '@/lib/betexplorer-types'
import * as cheerio from 'cheerio'

/**
 * API Route SSE para streaming de dados em tempo real do BetExplorer
 * Elimina loops infinitos usando conexão persistente
 */

interface LiveStreamData {
  matches: DashboardMatch[]
  totalMatches: number
  lastUpdated: string
  footballOnly: boolean
}

// Cache para detectar mudanças
let lastDataHash: string = ''
let cachedData: LiveStreamData | null = null

export async function GET(request: NextRequest) {
  console.log('🔄 Iniciando stream SSE para BetExplorer (todos os jogos)...')

  // Configurar headers SSE
  const headers = new Headers({
    'Content-Type': 'text/event-stream; charset=utf-8',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control, Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'X-Accel-Buffering': 'no', // Nginx
  })

  // Criar stream
  const stream = new ReadableStream({
    start(controller) {
      // Função para enviar dados (usando evento padrão 'message')
      const sendData = (data: LiveStreamData, eventType = 'data') => {
        try {
          // Usar formato padrão SSE sem evento customizado
          const message = `data: ${JSON.stringify({ type: eventType, ...data })}\n\n`
          console.log(`📤 Enviando dados ${eventType}:`, data.matches.length, 'jogos')
          controller.enqueue(new TextEncoder().encode(message))
        } catch {
          console.log('⚠️ Controller já fechado, ignorando envio')
        }
      }

      // Função para buscar e comparar dados
      const fetchAndCompare = async () => {
        try {
          console.log('🔍 Iniciando busca de dados...')
          const newData = await fetchLiveData()
          const newDataHash = JSON.stringify(newData)

          console.log('📊 Dados obtidos:', {
            matches: newData.matches.length,
            hash: newDataHash.substring(0, 50) + '...'
          })

          // Só enviar se houve mudanças
          if (newDataHash !== lastDataHash) {
            console.log('📡 Enviando dados atualizados via SSE')
            sendData(newData)
            lastDataHash = newDataHash
            cachedData = newData
          } else {
            console.log('⏸️ Dados inalterados, não enviando')
          }
        } catch (error) {
          console.error('❌ Erro ao buscar dados:', error)

          // Usar dados do cache se disponível em caso de erro
          if (cachedData) {
            console.log('⚠️ Usando dados do cache devido ao erro')
            sendData(cachedData, 'cached')
          } else {
            // Enviar dados de fallback se não houver cache
            const fallbackData: LiveStreamData = {
              matches: [],
              totalMatches: 0,
              lastUpdated: new Date().toISOString(),
              footballOnly: true
            }
            sendData(fallbackData, 'fallback')
          }
        }
      }

      // Enviar dados de teste imediatamente
      console.log('🚀 Enviando dados de teste...')
      const testData: LiveStreamData = {
        matches: [],
        totalMatches: 0,
        lastUpdated: new Date().toISOString(),
        footballOnly: true
      }
      sendData(testData, 'initial')

      // Enviar dados iniciais se disponíveis
      if (cachedData) {
        console.log('📦 Enviando dados do cache inicialmente')
        sendData(cachedData, 'initial')
      }

      // Buscar dados imediatamente
      fetchAndCompare()

      // Configurar intervalo para monitorar mudanças (30 segundos)
      const interval = setInterval(fetchAndCompare, 5000)

      // Cleanup quando conexão for fechada
      request.signal.addEventListener('abort', () => {
        console.log('🛑 Conexão SSE fechada')
        clearInterval(interval)
        controller.close()
      })

      // Heartbeat para manter conexão viva
      const heartbeat = setInterval(() => {
        const message = `event: heartbeat\ndata: ${JSON.stringify({ timestamp: new Date().toISOString() })}\n\n`
        controller.enqueue(new TextEncoder().encode(message))
      }, 15000) // A cada 15 segundos

      request.signal.addEventListener('abort', () => {
        clearInterval(heartbeat)
      })
    }
  })

  return new Response(stream, { headers })
}

// Função para buscar dados do BetExplorer (todos os jogos disponíveis)
async function fetchLiveData(): Promise<LiveStreamData> {
  // 1. Buscar live results
  const liveResultsUrl = `${BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/live-results.php`
  
  const liveResponse = await fetch(liveResultsUrl, {
    method: 'GET',
    headers: BETEXPLORER_CONFIG.HEADERS,
    signal: AbortSignal.timeout(10000)
  })

  if (!liveResponse.ok) {
    throw new Error(`Live Results API retornou status ${liveResponse.status}`)
  }

  const liveData = await liveResponse.json()
  
  if (!liveData || !liveData.events) {
    throw new Error('Dados de live results inválidos')
  }

  // 2. Filtrar apenas eventos de futebol (sem limite)
  const footballEvents = Object.entries(liveData.events)
    .filter(([, event]: [string, unknown]) => (event as { sport_id: number }).sport_id === SPORT_IDS.FOOTBALL)

  console.log(`⚽ Encontrados ${footballEvents.length} eventos de futebol`)

  // 3. Processar eventos com detalhes básicos (otimizado para velocidade)
  const matches: DashboardMatch[] = []

  for (let i = 0; i < footballEvents.length; i++) {
    const [eventId, eventData] = footballEvents[i] as [string, unknown]
    const event = eventData as {
      event_stage_id: string
      score: string
      minute: string
      finished: number
      sport_id: number
      live_odds_1x2?: unknown
      live_odds_ou?: unknown
      live_odds_dc?: unknown
      live_odds_dnb?: unknown
      live_odds_btts?: unknown
      live_odds_ah?: unknown
    }

    // Buscar informações do estágio
    const stageInfo = liveData.event_stages?.[event.event_stage_id]
    const stageName = stageInfo ? stageInfo[0] : 'Competição'

    // Tentar buscar detalhes completos incluindo logos
    let homeTeam = 'Time Casa'
    let awayTeam = 'Time Visitante'
    let homeTeamLogo: string | undefined
    let awayTeamLogo: string | undefined

    try {
      const details = await fetchMatchDetailsQuick(eventId)
      if (details) {
        homeTeam = details.homeTeam
        awayTeam = details.awayTeam
        homeTeamLogo = details.homeTeamLogo
        awayTeamLogo = details.awayTeamLogo

        console.log(`🏆 Detalhes obtidos para ${eventId}:`, {
          homeTeam,
          awayTeam,
          score: event.score,
          minute: event.minute,
          finished: event.finished,
          hasHomeLogo: !!homeTeamLogo,
          hasAwayLogo: !!awayTeamLogo
        })
      }
    } catch {
      console.log(`⚠️ Não foi possível buscar detalhes para ${eventId}, usando fallback`)
    }

    const match: DashboardMatch = {
      event_id: eventId,
      homeTeam,
      awayTeam,
      homeTeamLogo,
      awayTeamLogo,
      score: event.score || '0:0',
      minute: parseInt(event.minute) || 0,
      finished: event.finished === 1,
      competition: stageName,
      country: '',
      isLive: event.finished === 0 && parseInt(event.minute) > 0,
      odds: {
        live_odds_1x2: event.live_odds_1x2 as Record<string, number[]> | undefined,
        live_odds_ou: event.live_odds_ou as Record<string, number[]> | undefined,
        live_odds_dc: event.live_odds_dc as Record<string, number[]> | undefined,
        live_odds_dnb: event.live_odds_dnb as Record<string, number[]> | undefined,
        live_odds_btts: event.live_odds_btts as Record<string, number[]> | undefined,
        live_odds_ah: event.live_odds_ah as Record<string, number[]> | undefined
      }
    }

    matches.push(match)

    // Rate limiting entre requisições
    if (i < footballEvents.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 200))
    }
  }

  return {
    matches,
    totalMatches: matches.length,
    lastUpdated: new Date().toISOString(),
    footballOnly: true
  }
}

// Função otimizada para buscar detalhes completos incluindo logos
async function fetchMatchDetailsQuick(eventId: string) {
  try {
    const apiUrl = `${BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/match-content.php?e=${eventId}&t=new&bt=1x2&lang=en`

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: BETEXPLORER_CONFIG.HEADERS,
      signal: AbortSignal.timeout(5000) // Timeout um pouco maior para buscar logos
    })

    if (!response.ok) {
      return null
    }

    const htmlContent = await response.text()

    if (!htmlContent) {
      return null
    }

    // Parse do HTML para extrair nomes dos times e logos
    const $ = cheerio.load(htmlContent)

    const teamNames: string[] = []
    const teamLogos: string[] = []

    // Buscar nomes dos times
    $('h3').each((index, element) => {
      if (index < 2) {
        const teamName = $(element).text().trim()
        if (teamName) {
          teamNames.push(teamName)
        }
      }
    })

    // Buscar logos dos times
    $('.team-logo img, .logo img, img[src*="team"], img[src*="logo"]').each((index, element) => {
      if (index < 2) {
        const logoSrc = $(element).attr('src')
        if (logoSrc) {
          // Converter URL relativa para absoluta se necessário
          const fullLogoUrl = logoSrc.startsWith('http')
            ? logoSrc
            : `${BETEXPLORER_CONFIG.BASE_URL}${logoSrc.startsWith('/') ? '' : '/'}${logoSrc}`
          teamLogos.push(fullLogoUrl)
        }
      }
    })

    // Buscar logos alternativos se não encontrou
    if (teamLogos.length === 0) {
      $('img').each((_, element) => {
        const src = $(element).attr('src')
        const alt = $(element).attr('alt')

        if (src && (
          src.includes('team') ||
          src.includes('logo') ||
          src.includes('club') ||
          (alt && (alt.includes('logo') || alt.includes('team')))
        )) {
          const fullLogoUrl = src.startsWith('http')
            ? src
            : `${BETEXPLORER_CONFIG.BASE_URL}${src.startsWith('/') ? '' : '/'}${src}`
          teamLogos.push(fullLogoUrl)

          if (teamLogos.length >= 2) return false // Parar após encontrar 2
        }
      })
    }

    if (teamNames.length >= 2) {
      return {
        homeTeam: teamNames[0],
        awayTeam: teamNames[1],
        homeTeamLogo: teamLogos[0] || undefined,
        awayTeamLogo: teamLogos[1] || undefined
      }
    }

    return null

  } catch (error) {
    console.error(`❌ Erro ao buscar detalhes do evento ${eventId}:`, error)
    return null
  }
}

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
