import { NextRequest, NextResponse } from 'next/server'
import { 
  DashboardMatch,
  BetExplorerResponse, 
  BETEXPLORER_CONFIG,
  SPORT_IDS 
} from '@/lib/betexplorer-types'
import * as cheerio from 'cheerio'

/**
 * API Route otimizada que carrega live results + detalhes dos jogos em uma única chamada
 * Evita loops infinitos e múltiplas requisições
 */

interface LiveResultsLoadResponse {
  matches: DashboardMatch[]
  totalMatches: number
  lastUpdated: string
  footballOnly: boolean
}

// Cache global para evitar requisições desnecessárias
let globalCache: {
  data: LiveResultsLoadResponse | null
  timestamp: number
} = {
  data: null,
  timestamp: 0
}

// Sistema de debounce para evitar múltiplas requisições simultâneas
let ongoingRequest: Promise<LiveResultsLoadResponse> | null = null

const CACHE_TTL = 60000 // 1 minuto de cache para dados combinados

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const maxMatches = parseInt(searchParams.get('maxMatches') || '4')
    const forceRefresh = searchParams.get('refresh') === 'true'

    // Verificar cache
    const now = Date.now()
    const cacheValid = globalCache.data && (now - globalCache.timestamp) < CACHE_TTL

    if (cacheValid && !forceRefresh) {
      console.log('📦 Retornando dados combinados do cache')
      return NextResponse.json({
        success: true,
        data: globalCache.data,
        timestamp: new Date().toISOString(),
        cached: true
      } as BetExplorerResponse<LiveResultsLoadResponse>)
    }

    // Sistema de debounce: se já há uma requisição em andamento, aguardar ela
    if (ongoingRequest && !forceRefresh) {
      console.log('⏳ Aguardando requisição em andamento...')
      const data = await ongoingRequest
      return NextResponse.json({
        success: true,
        data,
        timestamp: new Date().toISOString(),
        cached: false
      } as BetExplorerResponse<LiveResultsLoadResponse>)
    }

    console.log('🔄 Buscando dados combinados do BetExplorer...')

    // Criar promise para a requisição atual
    ongoingRequest = fetchCombinedData(maxMatches)

    try {
      const data = await ongoingRequest

      // Atualizar cache
      globalCache = {
        data,
        timestamp: now
      }

      console.log(`✅ Dados combinados processados: ${data.matches.length} jogos`)

      return NextResponse.json({
        success: true,
        data,
        timestamp: new Date().toISOString(),
        cached: false
      } as BetExplorerResponse<LiveResultsLoadResponse>)

    } finally {
      // Limpar requisição em andamento
      ongoingRequest = null
    }

  } catch (error) {
    console.error('❌ Erro na API live_results_load:', error)

    // Limpar requisição em andamento em caso de erro
    ongoingRequest = null

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro interno do servidor',
      timestamp: new Date().toISOString()
    } as BetExplorerResponse<LiveResultsLoadResponse>, { status: 500 })
  }
}

// Função separada para buscar dados combinados
async function fetchCombinedData(maxMatches: number): Promise<LiveResultsLoadResponse> {
  // 1. Buscar live results
  const liveResultsUrl = `${BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/live-results.php`
    
    const liveResponse = await fetch(liveResultsUrl, {
      method: 'GET',
      headers: BETEXPLORER_CONFIG.HEADERS,
      signal: AbortSignal.timeout(10000)
    })

    if (!liveResponse.ok) {
      throw new Error(`Live Results API retornou status ${liveResponse.status}`)
    }

    const liveData = await liveResponse.json()
    
    if (!liveData || !liveData.events) {
      throw new Error('Dados de live results inválidos')
    }

    // 2. Filtrar apenas eventos de futebol e pegar os primeiros N
    const footballEvents = Object.entries(liveData.events)
      .filter(([, event]: [string, unknown]) => (event as { sport_id: number }).sport_id === SPORT_IDS.FOOTBALL)
      .slice(0, maxMatches)

    console.log(`⚽ Encontrados ${footballEvents.length} eventos de futebol`)

    // 3. Buscar detalhes para cada evento (com rate limiting)
    const matches: DashboardMatch[] = []
    
    for (let i = 0; i < footballEvents.length; i++) {
      const [eventId, eventData] = footballEvents[i] as [string, unknown]
      const event = eventData as {
        event_stage_id: string
        score: string
        minute: string
        finished: number
        sport_id: number
        live_odds_1x2?: unknown
        live_odds_ou?: unknown
        live_odds_dc?: unknown
        live_odds_dnb?: unknown
        live_odds_btts?: unknown
        live_odds_ah?: unknown
      }
      
      try {
        // Rate limiting: aguardar 500ms entre requisições
        if (i > 0) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }

        // Buscar detalhes do jogo
        const matchDetails = await fetchMatchDetails(eventId)
        
        // Buscar informações do estágio
        const stageInfo = liveData.event_stages?.[event.event_stage_id]
        const stageName = stageInfo ? stageInfo[0] : 'Competição'

        // Construir objeto DashboardMatch
        const match: DashboardMatch = {
          event_id: eventId,
          homeTeam: matchDetails?.homeTeam || 'Time Casa',
          awayTeam: matchDetails?.awayTeam || 'Time Visitante',
          homeTeamLogo: undefined, // Não disponível nesta API
          awayTeamLogo: undefined, // Não disponível nesta API
          score: event.score || '0:0',
          minute: parseInt(event.minute) || 0,
          finished: event.finished === 1,
          competition: matchDetails?.tournament || stageName,
          country: matchDetails?.country || '',
          isLive: event.finished === 0 && parseInt(event.minute) > 0,
          odds: {
            live_odds_1x2: event.live_odds_1x2 as Record<string, number[]> | undefined,
            live_odds_ou: event.live_odds_ou as Record<string, number[]> | undefined,
            live_odds_dc: event.live_odds_dc as Record<string, number[]> | undefined,
            live_odds_dnb: event.live_odds_dnb as Record<string, number[]> | undefined,
            live_odds_btts: event.live_odds_btts as Record<string, number[]> | undefined,
            live_odds_ah: event.live_odds_ah as Record<string, number[]> | undefined
          }
        }

        matches.push(match)
        console.log(`✅ Processado: ${match.homeTeam} x ${match.awayTeam}`)

      } catch (error) {
        console.error(`❌ Erro ao processar evento ${eventId}:`, error)
        
        // Criar match com dados básicos em caso de erro
        const fallbackMatch: DashboardMatch = {
          event_id: eventId,
          homeTeam: 'Time Casa',
          awayTeam: 'Time Visitante',
          score: event.score || '0:0',
          minute: parseInt(event.minute) || 0,
          finished: event.finished === 1,
          competition: 'Competição',
          country: '',
          isLive: event.finished === 0,
          odds: {}
        }
        
        matches.push(fallbackMatch)
      }
    }

    // 4. Construir e retornar resposta
    return {
      matches,
      totalMatches: matches.length,
      lastUpdated: new Date().toISOString(),
      footballOnly: true
    }
}

// Função auxiliar para buscar detalhes de um jogo
async function fetchMatchDetails(eventId: string) {
  try {
    const apiUrl = `${BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/match-content.php?e=${eventId}&t=new&bt=1x2&lang=en`
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: BETEXPLORER_CONFIG.HEADERS,
      signal: AbortSignal.timeout(8000)
    })

    if (!response.ok) {
      throw new Error(`Match content API retornou status ${response.status}`)
    }

    const htmlContent = await response.text()
    
    if (!htmlContent) {
      throw new Error('HTML vazio')
    }

    // Parse básico do HTML
    const $ = cheerio.load(htmlContent)
    
    const teamNames: string[] = []
    $('h3').each((index, element) => {
      if (index < 2) {
        const teamName = $(element).text().trim()
        if (teamName) {
          teamNames.push(teamName)
        }
      }
    })

    if (teamNames.length >= 2) {
      return {
        homeTeam: teamNames[0],
        awayTeam: teamNames[1],
        tournament: 'Competição',
        country: ''
      }
    }

    return null

  } catch (error) {
    console.error(`Erro ao buscar detalhes do evento ${eventId}:`, error)
    return null
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
export const revalidate = 0
