import { NextRequest, NextResponse } from 'next/server'
import { 
  MatchDetails, 
  BetExplorerResponse, 
  BETEXPLORER_CONFIG 
} from '@/lib/betexplorer-types'
import * as cheerio from 'cheerio'

/**
 * API Route para Match Content do BetExplorer
 * Extrai informações detalhadas de jogos específicos incluindo nomes dos times
 */

// Cache para detalhes de jogos (válido por mais tempo que live results)
const matchCache = new Map<string, {
  data: MatchDetails
  timestamp: number
}>()

const MATCH_CACHE_TTL = 5 * 60 * 1000 // 5 minutos

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')
    const template = searchParams.get('template') || 'new'
    const bettingType = searchParams.get('bettingType') || '1x2'
    const token = searchParams.get('token') // Token de sessão opcional
    const lang = searchParams.get('lang') || 'en'

    if (!eventId) {
      return NextResponse.json({
        success: false,
        error: 'eventId é obrigatório',
        timestamp: new Date().toISOString()
      } as BetExplorerResponse, { status: 400 })
    }

    // Verificar cache
    const cached = matchCache.get(eventId)
    const now = Date.now()
    
    if (cached && (now - cached.timestamp) < MATCH_CACHE_TTL) {
      console.log(`📦 Retornando dados do cache - Match Content: ${eventId}`)
      return NextResponse.json({
        success: true,
        data: cached.data,
        timestamp: new Date().toISOString(),
        cached: true
      } as BetExplorerResponse<MatchDetails>)
    }

    console.log(`🔄 Buscando dados do BetExplorer - Match Content: ${eventId}`)

    // Construir URL da API
    let apiUrl = `${BETEXPLORER_CONFIG.BASE_URL}/gres/ajax/match-content.php`
    const params = new URLSearchParams({
      e: eventId,
      t: template,
      bt: bettingType,
      lang: lang
    })
    
    if (token) {
      params.append('ts', token)
    }
    
    apiUrl += `?${params.toString()}`

    // Fazer requisição para BetExplorer
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: BETEXPLORER_CONFIG.HEADERS,
      signal: AbortSignal.timeout(15000) // 15 segundos para parsing HTML
    })

    if (!response.ok) {
      throw new Error(`BetExplorer API retornou status ${response.status}`)
    }

    const htmlContent = await response.text()
    
    if (!htmlContent || htmlContent.trim().length === 0) {
      throw new Error('Resposta HTML vazia da API do BetExplorer')
    }

    // Fazer parsing do HTML usando Cheerio
    const $ = cheerio.load(htmlContent)
    
    // Extrair informações dos times
    const teamNames: string[] = []
    $('h3').each((index, element) => {
      if (index < 2) { // Primeiros 2 h3 são os nomes dos times
        const teamName = $(element).text().trim()
        if (teamName) {
          teamNames.push(teamName)
        }
      }
    })

    // Extrair logos dos times
    const teamLogos: string[] = []
    $('img[src*="/team-logo/"]').each((index, element) => {
      if (index < 2) { // Primeiros 2 logos
        const logoSrc = $(element).attr('src')
        if (logoSrc) {
          // Converter para URL absoluta se necessário
          const fullLogoUrl = logoSrc.startsWith('http') 
            ? logoSrc 
            : `${BETEXPLORER_CONFIG.BASE_URL}${logoSrc}`
          teamLogos.push(fullLogoUrl)
        }
      }
    })

    // Extrair informações da competição
    let tournament = ''
    let country = ''
    
    // Buscar por padrões conhecidos no HTML
    const htmlText = $.root().text()
    
    // Padrões comuns de competições
    const competitionPatterns = [
      /Champions League/i,
      /Premier League/i,
      /La Liga/i,
      /Serie A/i,
      /Bundesliga/i,
      /Ligue 1/i,
      /Copa do Mundo/i,
      /Eurocopa/i,
      /Copa América/i
    ]
    
    for (const pattern of competitionPatterns) {
      const match = htmlText.match(pattern)
      if (match) {
        tournament = match[0]
        break
      }
    }

    // Tentar extrair país (simplificado)
    const countryPatterns = [
      /England/i,
      /Spain/i,
      /Italy/i,
      /Germany/i,
      /France/i,
      /Brazil/i,
      /Argentina/i
    ]
    
    for (const pattern of countryPatterns) {
      const match = htmlText.match(pattern)
      if (match) {
        country = match[0]
        break
      }
    }

    // Validar dados extraídos
    if (teamNames.length < 2) {
      throw new Error('Não foi possível extrair nomes dos times do HTML')
    }

    // Construir objeto de resposta
    const matchDetails: MatchDetails = {
      event_id: eventId,
      homeTeam: teamNames[0],
      awayTeam: teamNames[1],
      homeTeamLogo: teamLogos[0] || undefined,
      awayTeamLogo: teamLogos[1] || undefined,
      tournament: tournament || 'Competição não identificada',
      country: country || 'País não identificado'
    }

    // Salvar no cache
    matchCache.set(eventId, {
      data: matchDetails,
      timestamp: now
    })

    console.log(`✅ Match Content processado: ${matchDetails.homeTeam} x ${matchDetails.awayTeam}`)

    return NextResponse.json({
      success: true,
      data: matchDetails,
      timestamp: new Date().toISOString(),
      cached: false
    } as BetExplorerResponse<MatchDetails>)

  } catch (error) {
    console.error('❌ Erro na API Match Content:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      timestamp: new Date().toISOString()
    } as BetExplorerResponse, { status: 500 })
  }
}

// Limpar cache periodicamente (executar a cada 10 minutos)
setInterval(() => {
  const now = Date.now()
  for (const [eventId, cached] of matchCache.entries()) {
    if (now - cached.timestamp > MATCH_CACHE_TTL) {
      matchCache.delete(eventId)
    }
  }
}, 10 * 60 * 1000)

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
export const revalidate = 0
