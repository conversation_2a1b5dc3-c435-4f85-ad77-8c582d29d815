import { NextRequest } from 'next/server'

/**
 * Rota de teste SSE simples para debugar
 */

export async function GET(request: NextRequest) {
  console.log('🧪 Iniciando teste SSE...')

  // Configurar headers SSE
  const headers = new Headers({
    'Content-Type': 'text/event-stream; charset=utf-8',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control, Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
  })

  // Criar stream
  const stream = new ReadableStream({
    start(controller) {
      console.log('🚀 Stream iniciado')

      // Enviar mensagem de teste imediatamente
      const testMessage = `data: ${JSON.stringify({ message: 'Teste SSE funcionando!', timestamp: new Date().toISOString() })}\n\n`
      controller.enqueue(new TextEncoder().encode(testMessage))
      console.log('📤 Mensagem de teste enviada')

      // Enviar mensagens a cada 2 segundos
      let counter = 0
      const interval = setInterval(() => {
        counter++
        const message = `data: ${JSON.stringify({ 
          message: `Mensagem ${counter}`, 
          timestamp: new Date().toISOString() 
        })}\n\n`
        
        try {
          controller.enqueue(new TextEncoder().encode(message))
          console.log(`📤 Mensagem ${counter} enviada`)
        } catch (error) {
          console.log('❌ Erro ao enviar mensagem:', error)
          clearInterval(interval)
          controller.close()
        }
      }, 2000)

      // Cleanup quando conexão for fechada
      request.signal.addEventListener('abort', () => {
        console.log('🛑 Conexão de teste SSE fechada')
        clearInterval(interval)
        controller.close()
      })
    }
  })

  return new Response(stream, { headers })
}

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'
