@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

/* Estilos globais básicos */
* {
  box-sizing: border-box;
}

/* Estilos para tooltip do sidebar recolhido */
.tooltip-trigger {
  position: relative;
}

.tooltip-trigger:hover::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 8px;
  padding: 4px 8px;
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  border: 1px solid hsl(var(--border));
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  animation: tooltip-fade-in 0.2s ease-in-out forwards;
}

@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-4px);
  }

  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* Reset apenas para elementos específicos que precisam */
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
li {
  margin: 0;
  padding: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  /* Definindo modo escuro como padrão */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: #08d285;
  --primary-foreground: #ffffff;
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

.light {
  /* Modo claro agora é opcional */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #10b981;
  --primary-foreground: #ffffff;
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {

  /* Classe utilitária para fonte monospace */
  .font-mono {
    font-family: var(--font-mono), ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  }

  /* Otimizações de tipografia para melhor legibilidade */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Classes para diferentes pesos da fonte Inter */
  .font-inter-light {
    font-family: var(--font-inter);
    font-weight: 300;
  }

  .font-inter-normal {
    font-family: var(--font-inter);
    font-weight: 400;
  }

  .font-inter-medium {
    font-family: var(--font-inter);
    font-weight: 500;
  }

  .font-inter-semibold {
    font-family: var(--font-inter);
    font-weight: 600;
  }

  .font-inter-bold {
    font-family: var(--font-inter);
    font-weight: 700;
  }

  /* Classes para cores primárias em textos */
  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  /* Variações de intensidade da cor primária para modo escuro */
  .text-primary-light {
    color: #2dd4aa;
    /* Verde mais claro que #08d285 */
  }

  .text-primary-muted {
    color: #06a66b;
    /* Verde mais escuro que #08d285 */
  }

  .text-primary-subtle {
    color: #047857;
    /* Verde bem sutil */
  }

  /* Classes para hover states */
  .hover\:text-primary:hover {
    color: var(--primary);
  }

  .hover\:text-primary-light:hover {
    color: #2dd4aa;
  }

  /* Classes para backgrounds com cor primária */
  .bg-primary-subtle {
    background-color: rgba(8, 210, 133, 0.1);
    /* 10% do verde escuro */
  }

  .bg-primary-light {
    background-color: rgba(8, 210, 133, 0.2);
    /* 20% do verde escuro */
  }

  /* Variações específicas para modo claro */
  .light .text-primary-light {
    color: #059669;
    /* Verde mais escuro para modo claro */
  }

  .light .text-primary-muted {
    color: #047857;
    /* Verde ainda mais escuro */
  }

  .light .text-primary-subtle {
    color: #065f46;
    /* Verde bem escuro para contraste */
  }

  .light .hover\:text-primary-light:hover {
    color: #059669;
  }

  .light .bg-primary-subtle {
    background-color: rgba(16, 185, 129, 0.1);
    /* 10% do verde claro */
  }

  .light .bg-primary-light {
    background-color: rgba(16, 185, 129, 0.2);
    /* 20% do verde claro */
  }
}

/* Scrollbar personalizado - fino e na cor bg-primary (verde) */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #10b981 transparent; /* Verde primary */
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #10b981; /* Verde primary */
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #059669; /* Verde primary mais escuro no hover */
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}