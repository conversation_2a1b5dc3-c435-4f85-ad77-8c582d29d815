import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import ConditionalLayout from "@/components/layout/ConditionalLayout";

// Fonte principal otimizada para performance
const inter = Inter({
  subsets: ["latin"],
  display: "swap", // Evita bloqueio de renderização
  variable: "--font-inter",
  preload: true,
});

// Fonte monospace para código e elementos técnicos
const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-mono",
  preload: false, // Carregamento sob demanda
});

export const metadata: Metadata = {
  title: "Frontend - Projeto Profit Growth",
  description: "Frontend do projeto Profit Growth",
  icons: {
    icon: [
      {
        url: '/icon-logo.svg',
        type: 'image/svg+xml',
      },
    ],
    shortcut: '/icon-logo.svg',
    apple: '/icon-logo.svg',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR" className={`${inter.variable} ${jetbrainsMono.variable}`}>
      <body className={`${inter.className} antialiased`}>
        <ConditionalLayout>
          {children}
        </ConditionalLayout>
      </body>
    </html>
  );
}
