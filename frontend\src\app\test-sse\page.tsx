'use client'

import { useTestSSE } from '@/hooks/useTestSSE'

export default function TestSSE() {
  const { messages, connectionStatus } = useTestSSE()

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Teste SSE</h1>
      
      <div className="mb-4">
        <strong>Status da Conexão:</strong> 
        <span className={`ml-2 px-2 py-1 rounded text-sm ${
          connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
          connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
          connectionStatus === 'error' ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {connectionStatus}
        </span>
      </div>

      <div>
        <strong>Mensagens Recebidas:</strong>
        <div className="mt-2 p-4 bg-gray-100 rounded max-h-96 overflow-y-auto">
          {messages.length === 0 ? (
            <p className="text-gray-500">Nenhuma mensagem recebida ainda...</p>
          ) : (
            messages.map((message, index) => (
              <div key={index} className="mb-1 font-mono text-sm">
                {message}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
