'use client';

import InfoMenu from "@/components/info-menu"
import NotificationMenu from "@/components/notification-menu"
import UserMenu from "@/components/user-menu"
import PortfolioBalance from "@/components/PortfolioBalance"
import { ThemeToggle } from "@/components/ThemeToggle"

export default function Navbar() {
  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex h-16 items-center justify-between gap-4">
          {/* Lado esquerdo - UserMenu e PortfolioBalance */}
          <div className="flex items-center gap-4">
          
            
           
            
            {/* Portfolio Balance */}
            <PortfolioBalance />
          </div>

          {/* Centro - Espaço flexível */}
          <div className="flex-1"></div>

          {/* Lado direito */}
          <div className="flex items-center gap-2">
              {/* Alternador de tema */}
              <ThemeToggle />
            {/* Notificações */}
            <NotificationMenu />
            
            {/* Menu de informações */}
            <InfoMenu />
            
          
              {/* Menu do usuário */}
              <UserMenu />
          </div>
        </div>
      </div>
    </header>
  )
}
