'use client'

import { useState, useEffect, useMemo } from 'react'
import { TrendingUpIcon, TrendingDownIcon, RefreshCwIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface BalanceData {
    portfolioBalance: number
    availableFunds: number
    isLoading: boolean
    trend: 'up' | 'down' | 'neutral'
    changePercent: number
    lastUpdated: Date
}

export default function PortfolioBalance() {
    const [balanceData, setBalanceData] = useState<BalanceData>({
        portfolioBalance: 623098.17,
        availableFunds: 122912.50,
        isLoading: false,
        trend: 'up',
        changePercent: 2.34,
        lastUpdated: new Date()
    })

    const [isRefreshing, setIsRefreshing] = useState(false)

    // Função otimizada para formatar valores monetários
    const formatarMoeda = useMemo(() => {
        const formatter = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        })
        return (valor: number) => formatter.format(valor)
    }, [])

    // Função para atualizar dados manualmente
    const atualizarDados = async () => {
        setIsRefreshing(true)

        // Simular chamada de API
        await new Promise(resolve => setTimeout(resolve, 1000))

        setBalanceData(prev => ({
            ...prev,
            portfolioBalance: prev.portfolioBalance + (Math.random() - 0.5) * 2000,
            availableFunds: prev.availableFunds + (Math.random() - 0.5) * 1000,
            changePercent: (Math.random() - 0.5) * 8,
            trend: Math.random() > 0.5 ? 'up' : 'down',
            lastUpdated: new Date()
        }))

        setIsRefreshing(false)
    }

    // Atualização automática a cada 60 segundos
    useEffect(() => {
        const interval = setInterval(() => {
            if (!isRefreshing) {
                setBalanceData(prev => ({
                    ...prev,
                    portfolioBalance: prev.portfolioBalance + (Math.random() - 0.5) * 500,
                    availableFunds: prev.availableFunds + (Math.random() - 0.5) * 200,
                    changePercent: prev.changePercent + (Math.random() - 0.5) * 0.5,
                    trend: Math.random() > 0.6 ? (Math.random() > 0.5 ? 'up' : 'down') : prev.trend,
                    lastUpdated: new Date()
                }))
            }
        }, 60000)

        return () => clearInterval(interval)
    }, [isRefreshing])

    if (balanceData.isLoading) {
        return (
            <div className="flex items-center gap-4 px-4 py-2 bg-card/30 rounded-lg border animate-pulse">
                <div className="flex flex-col gap-1">
                    <div className="h-3 bg-muted rounded w-20"></div>
                    <div className="h-5 bg-muted rounded w-28"></div>
                </div>
                <div className="h-6 w-px bg-border"></div>
                <div className="flex flex-col gap-1">
                    <div className="h-3 bg-muted rounded w-24"></div>
                    <div className="h-5 bg-muted rounded w-24"></div>
                </div>
            </div>
        )
    }

    return (
        <div className="flex items-center gap-4 px-4 py-2 bg-card/30 rounded-lg  hover:bg-card/50 transition-all duration-200 group">
            {/* Portfolio Balance */}
            <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1 mb-0.5">
                    <span className="text-xs  text-muted-foreground  tracking-wider">
                        Saldo Inicial
                    </span>
                </div>
                <div className="flex items-center gap-2">
                    <span className="text-sm  text-foreground tabular-nums truncate">
                        {formatarMoeda(balanceData.portfolioBalance)}
                    </span>
                    <div className={`flex items-center gap-1 px-1.5 py-0.5 rounded-full text-xs font-medium transition-colors ${balanceData.trend === 'up'
                        ? 'text-primary  dark:text-primary '
                        : 'text-red-700 bg-red-100 dark:text-red-300 dark:bg-red-900/30'
                        }`}>
                        {balanceData.trend === 'up' ? (
                            <TrendingUpIcon size={10} />
                        ) : (
                            <TrendingDownIcon size={10} />
                        )}
                        <span>{Math.abs(balanceData.changePercent).toFixed(2)}%</span>
                    </div>
                </div>
            </div>

            {/* Separador vertical */}
            <div className="h-8 w-px bg-border/60"></div>

            {/* Available Funds */}
            <div className="flex flex-col min-w-0">
                <div className="flex items-center gap-1 mb-0.5">
                    <span className="text-xs  text-muted-foreground  tracking-wider">
                        Saldo Atual
                    </span>
                </div>
                <span className="text-sm  text-foreground tabular-nums truncate">
                    {formatarMoeda(balanceData.availableFunds)}
                </span>
            </div>

            {/* Botão de atualização */}
            <div className="flex items-center gap-2 ml-2">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={atualizarDados}
                    disabled={isRefreshing}
                    className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                    <RefreshCwIcon
                        size={14}
                        className={`${isRefreshing ? 'animate-spin' : ''} text-muted-foreground hover:text-foreground`}
                    />
                </Button>

                
            </div>
        </div>
    )
}