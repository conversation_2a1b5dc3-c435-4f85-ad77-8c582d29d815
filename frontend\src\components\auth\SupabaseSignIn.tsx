'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useAuth } from '@/hooks/useAuth'
import { CanvasRevealEffect } from '@/components/ui/sign-in-flow-1'

interface SupabaseSignInProps {
  className?: string
}

const AnimatedNavLink = ({ href, children }: { href: string; children: React.ReactNode }) => {
  const defaultTextColor = 'text-gray-300';
  const hoverTextColor = 'text-white';
  const textSizeClass = 'text-sm';

  return (
    <a href={href} className={`group relative inline-block overflow-hidden h-5 flex items-center ${textSizeClass}`}>
      <div className="flex flex-col transition-transform duration-400 ease-out transform group-hover:-translate-y-1/2">
        <span className={defaultTextColor}>{children}</span>
        <span className={hoverTextColor}>{children}</span>
      </div>
    </a>
  );
};

const MiniNavbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [headerShapeClass, setHeaderShapeClass] = useState('rounded-full')
  const shapeTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  useEffect(() => {
    if (shapeTimeoutRef.current) {
      clearTimeout(shapeTimeoutRef.current)
    }
    if (isOpen) {
      setHeaderShapeClass('rounded-xl')
    } else {
      shapeTimeoutRef.current = setTimeout(() => {
        setHeaderShapeClass('rounded-full')
      }, 300)
    }
    return () => {
      if (shapeTimeoutRef.current) {
        clearTimeout(shapeTimeoutRef.current)
      }
    }
  }, [isOpen])



  const navLinksData = [
    { label: 'Planos', href: '#1' },
    { label: 'Ferramentas', href: '#2' },
    { label: 'Contato', href: '#3' },
  ];

  const loginButtonElement = (
    <button className="px-4 py-2 sm:px-3 text-xs sm:text-sm border border-[#333] bg-[rgba(31,31,31,0.62)] text-gray-300 rounded-full hover:border-white/50 hover:text-white transition-colors duration-200 w-full sm:w-auto">
      Entrar
    </button>
  );

  const signupButtonElement = (
    <div className="relative group w-full sm:w-auto">
      <div className="absolute inset-0 -m-2 rounded-full hidden sm:block bg-green-100 opacity-40 filter blur-lg pointer-events-none transition-all duration-300 ease-out group-hover:opacity-60 group-hover:blur-xl group-hover:-m-3"></div>
      <button className="relative z-10 px-4 py-2 sm:px-3 text-xs sm:text-sm font-semibold text-black bg-gradient-to-br from-green-100 to-green-400 rounded-full hover:from-green-200 hover:to-green-400 transition-all duration-200 w-full sm:w-auto">
        Inscreva-se
      </button>
    </div>
  );

  return (
    <header className={`fixed top-6 left-1/2 transform -translate-x-1/2 z-20 flex flex-col items-center pl-6 pr-6 py-3 backdrop-blur-sm ${headerShapeClass} border border-[#333] bg-[#1f1f1f57] w-[calc(100%-2rem)] sm:w-auto transition-[border-radius] duration-0 ease-in-out`}>
      <div className="flex items-center justify-between w-full gap-x-6 sm:gap-x-8">
        <div className="flex items-center">
          <Image
            src="/logo.svg"
            alt="Logo"
            width={144}
            height={20}
            className="w-36 h-5"
          />
        </div>

        <nav className="hidden sm:flex items-center space-x-4 sm:space-x-6 text-sm">
          {navLinksData.map((link) => (
            <AnimatedNavLink key={link.href} href={link.href}>
              {link.label}
            </AnimatedNavLink>
          ))}
        </nav>

        <div className="hidden sm:flex items-center gap-2 sm:gap-3">
          {loginButtonElement}
          {signupButtonElement}
        </div>

        <button
          className="sm:hidden flex items-center justify-center w-8 h-8 text-gray-300 focus:outline-none"
          onClick={toggleMenu}
          aria-label={isOpen ? 'Close Menu' : 'Open Menu'}
        >
          {isOpen ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          )}
        </button>
      </div>

      <div className={`sm:hidden flex flex-col items-center w-full transition-all ease-in-out duration-300 overflow-hidden ${isOpen ? 'max-h-[1000px] opacity-100 pt-4' : 'max-h-0 opacity-0 pt-0 pointer-events-none'}`}>
        <nav className="flex flex-col items-center space-y-4 text-base w-full">
          {navLinksData.map((link) => (
            <a key={link.href} href={link.href} className="text-gray-300 hover:text-white transition-colors w-full text-center">
              {link.label}
            </a>
          ))}
        </nav>
        <div className="flex flex-col items-center space-y-4 mt-4 w-full">
          {loginButtonElement}
          {signupButtonElement}
        </div>
      </div>
    </header>
  )
}

export const SupabaseSignIn = ({ className }: SupabaseSignInProps) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [step, setStep] = useState<'email' | 'code' | 'success' | 'password' | 'signup-password'>('email')
  const [code, setCode] = useState(['', '', '', '', '', ''])
  const [mode, setMode] = useState<'signin' | 'signup'>('signin')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const codeInputRefs = useRef<(HTMLInputElement | null)[]>([])
  const [initialCanvasVisible, setInitialCanvasVisible] = useState(true)
  const [reverseCanvasVisible, setReverseCanvasVisible] = useState(false)

  const router = useRouter()
  const {
    signInWithEmail,
    signInWithGoogle,
    signUpWithEmail,
    verifyOTP,
    isLoading,
    error,
    user
  } = useAuth()

  // Redirecionar se já estiver logado
  useEffect(() => {
    if (user) {
      router.push('/dashboard')
    }
  }, [user, router])

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email) return

    if (mode === 'signin') {
      // Para login, vamos direto para senha
      setStep('password')
    } else {
      // Para signup, vamos para os campos de senha primeiro
      setStep('signup-password')
    }
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email || !password) return

    const result = await signInWithEmail(email, password)
    if (result.success) {
      setReverseCanvasVisible(true)
      setTimeout(() => {
        setInitialCanvasVisible(false)
      }, 50)
      setTimeout(() => {
        setStep('success')
      }, 2000)
    }
  }

  const handleSignupPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!email || !password || !confirmPassword) return

    if (password !== confirmPassword) {
      console.log('Senhas não coincidem')
      return
    }

    console.log('Iniciando processo de signup com email e senha...')

    // Registra o usuário com email e senha
    // O Supabase automaticamente enviará um email de confirmação
    const signupResult = await signUpWithEmail(email, password)
    if (signupResult.success) {
      console.log('Usuário criado com sucesso, email de confirmação enviado')
      setStep('code')
    } else {
      console.error('Erro no signup:', signupResult.error)
    }
  }

  const handleGoogleSignIn = async () => {
    await signInWithGoogle()
  }

  const handleModeChange = () => {
    const newMode = mode === 'signin' ? 'signup' : 'signin'
    setMode(newMode)
    // Reset all form states when changing mode
    setStep('email')
    setPassword('')
    setConfirmPassword('')
    setCode(['', '', '', '', '', ''])
    setShowPassword(false)
    setShowConfirmPassword(false)
    setReverseCanvasVisible(false)
    setInitialCanvasVisible(true)
  }

  useEffect(() => {
    if (step === 'code') {
      setTimeout(() => {
        codeInputRefs.current[0]?.focus()
      }, 500)
    }
  }, [step])

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newCode = [...code]
      newCode[index] = value
      setCode(newCode)

      if (value && index < 5) {
        codeInputRefs.current[index + 1]?.focus()
      }

      if (index === 5 && value) {
        const isComplete = newCode.every(digit => digit.length === 1)
        if (isComplete) {
          handleOTPVerification(newCode.join(''))
        }
      }
    }
  }

  const handleOTPVerification = async (otpCode: string) => {
    // Para signup, usamos tipo 'signup', para signin usamos 'email'
    const otpType = mode === 'signup' ? 'signup' : 'email'
    const result = await verifyOTP(email, otpCode, otpType)
    if (result.success) {
      setReverseCanvasVisible(true)
      setTimeout(() => {
        setInitialCanvasVisible(false)
      }, 50)
      setTimeout(() => {
        setStep('success')
      }, 2000)
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      codeInputRefs.current[index - 1]?.focus()
    }
  }

  const handleBackClick = () => {
    if (step === 'password') {
      setStep('email')
      setPassword('')
    } else if (step === 'signup-password') {
      setStep('email')
      setPassword('')
      setConfirmPassword('')
    } else if (step === 'code') {
      if (mode === 'signup') {
        setStep('signup-password')
      } else {
        setStep('email')
      }
      setCode(['', '', '', '', '', ''])
    }
    setReverseCanvasVisible(false)
    setInitialCanvasVisible(true)
  }

  return (
    <div className={cn('flex w-[100%] flex-col min-h-screen bg-black relative', className)}>
      <div className="absolute inset-0 z-0">
        {initialCanvasVisible && (
          <div className="absolute inset-0">
            <CanvasRevealEffect
              animationSpeed={3}
              containerClassName="bg-black"
              colors={[
                [8, 210, 133],
                [255, 255, 255],
              ]}
              dotSize={6}
              reverse={false}
            />
          </div>
        )}

        {reverseCanvasVisible && (
          <div className="absolute inset-0">
            <CanvasRevealEffect
              animationSpeed={4}
              containerClassName="bg-black"
              colors={[
                [8, 210, 133],
                [255, 255, 255],
              ]}
              dotSize={6}
              reverse={true}
            />
          </div>
        )}

        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(0,0,0,1)_0%,_transparent_100%)]" />
        <div className="absolute top-0 left-0 right-0 h-1/3 bg-gradient-to-b from-black to-transparent" />
      </div>

      <div className="relative z-10 flex flex-col flex-1">
        <MiniNavbar />

        <div className="flex flex-1 flex-col lg:flex-row">
          <div className="flex-1 flex flex-col justify-center items-center">
            <div className="w-full mt-[150px] max-w-sm">

              {/* Mostrar erro se houver */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm text-center"
                >
                  {error.message}
                </motion.div>
              )}

              <AnimatePresence mode="wait">
                {step === 'email' ? (
                  <motion.div
                    key="email-step"
                    initial={{ opacity: 0, x: -100 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -100 }}
                    transition={{ duration: 0.4, ease: 'easeOut' }}
                    className="space-y-6 text-center"
                  >
                    <div className="space-y-6">


                      <div className="space-y-1">
                        <h1 className="text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white">
                          {mode === 'signin' ? 'Bem-vindo de volta' : 'Criar conta'}
                        </h1>
                        <p className="text-[1.8rem] text-white/70 font-light">
                          {mode === 'signin' ? 'Entre na sua conta' : 'Junte-se a nós hoje'}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <button
                        onClick={handleGoogleSignIn}
                        disabled={isLoading}
                        className="backdrop-blur-[2px] w-full flex items-center justify-center gap-2 bg-white/5 hover:bg-white/10 text-white border border-white/10 rounded-full py-3 px-4 transition-colors disabled:opacity-50"
                      >
                        <span className="text-lg">G</span>
                        <span>{mode === 'signin' ? 'Entrar' : 'Registrar'} com Google</span>
                      </button>

                      <div className="flex items-center gap-4">
                        <div className="h-px bg-white/10 flex-1" />
                        <span className="text-white/40 text-sm">ou</span>
                        <div className="h-px bg-white/10 flex-1" />
                      </div>

                      <form onSubmit={handleEmailSubmit}>
                        <div className="relative">
                          <input
                            type="email"
                            placeholder="<EMAIL>"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            disabled={isLoading}
                            className="w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50"
                            required
                          />
                          <button
                            type="submit"
                            disabled={isLoading}
                            className="absolute right-1.5 top-1.5 text-white w-9 h-9 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-colors group overflow-hidden disabled:opacity-50"
                          >
                            <span className="relative w-full h-full block overflow-hidden">
                              <span className="absolute inset-0 flex items-center justify-center transition-transform duration-300 group-hover:translate-x-full">
                                →
                              </span>
                              <span className="absolute inset-0 flex items-center justify-center transition-transform duration-300 -translate-x-full group-hover:translate-x-0">
                                →
                              </span>
                            </span>
                          </button>
                        </div>
                      </form>
                    </div>

                    <div className="flex justify-center">
                      <button
                        onClick={handleModeChange}
                        className="text-white/50 hover:text-white/70 transition-colors text-sm"
                      >
                        {mode === 'signin' ? 'Não tem conta? Registre-se' : 'Já tem conta? Entre'}
                      </button>
                    </div>

                    <p className="text-xs text-white/40 pt-10">
                      Ao continuar, você concorda com os{' '}
                      <Link href="#" className="underline text-white/40 hover:text-white/60 transition-colors">
                        Termos de Serviço
                      </Link>{' '}
                      e{' '}
                      <Link href="#" className="underline text-white/40 hover:text-white/60 transition-colors">
                        Política de Privacidade
                      </Link>
                      .
                    </p>
                  </motion.div>
                ) : step === 'password' ? (
                  <motion.div
                    key="password-step"
                    initial={{ opacity: 0, x: 100 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 100 }}
                    transition={{ duration: 0.4, ease: 'easeOut' }}
                    className="space-y-6 text-center"
                  >
                    <div className="space-y-1">
                      <h1 className="text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white">
                        Digite sua senha
                      </h1>
                      <p className="text-[1.25rem] text-white/50 font-light">
                        Para {email}
                      </p>
                    </div>

                    <form onSubmit={handlePasswordSubmit} className="space-y-4">
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Sua senha"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          disabled={isLoading}
                          className="w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 text-sm"
                        >
                          {showPassword ? 'Ocultar' : 'Mostrar'}
                        </button>
                      </div>

                      <div className="flex w-full gap-3">
                        <motion.button
                          type="button"
                          onClick={handleBackClick}
                          className="rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          transition={{ duration: 0.2 }}
                        >
                          Voltar
                        </motion.button>
                        <motion.button
                          type="submit"
                          disabled={isLoading || !password}
                          className={`flex-1 rounded-full font-medium py-3 border transition-all duration-300 ${password && !isLoading
                            ? 'bg-white text-black border-transparent hover:bg-white/90 cursor-pointer'
                            : 'bg-[#111] text-white/50 border-white/10 cursor-not-allowed'
                            }`}
                        >
                          {isLoading ? 'Entrando...' : 'Entrar'}
                        </motion.button>
                      </div>
                    </form>
                  </motion.div>
                ) : step === 'signup-password' ? (
                  <motion.div
                    key="signup-password-step"
                    initial={{ opacity: 0, x: 100 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 100 }}
                    transition={{ duration: 0.4, ease: 'easeOut' }}
                    className="space-y-6 text-center"
                  >
                    <div className="space-y-1">
                      <h1 className="text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white">
                        Criar sua senha
                      </h1>
                      <p className="text-[1.25rem] text-white/50 font-light">
                        Para {email}
                      </p>
                    </div>

                    <form onSubmit={handleSignupPasswordSubmit} className="space-y-4">
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Sua senha"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          disabled={isLoading}
                          className="w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 text-sm"
                        >
                          {showPassword ? 'Ocultar' : 'Mostrar'}
                        </button>
                      </div>

                      <div className="relative">
                        <input
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder="Confirmar senha"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          disabled={isLoading}
                          className="w-full backdrop-blur-[1px] text-white border-1 border-white/10 rounded-full py-3 px-4 focus:outline-none focus:border focus:border-white/30 text-center disabled:opacity-50"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 text-sm"
                        >
                          {showConfirmPassword ? 'Ocultar' : 'Mostrar'}
                        </button>
                      </div>

                      {password && confirmPassword && password !== confirmPassword && (
                        <p className="text-red-400 text-sm">As senhas não coincidem</p>
                      )}

                      {error && (
                        <p className="text-red-400 text-sm">{error.message}</p>
                      )}

                      <div className="flex w-full gap-3">
                        <motion.button
                          type="button"
                          onClick={handleBackClick}
                          className="rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          transition={{ duration: 0.2 }}
                        >
                          Voltar
                        </motion.button>
                        <motion.button
                          type="submit"
                          disabled={isLoading || !password || !confirmPassword || password !== confirmPassword}
                          className={`flex-1 rounded-full font-medium py-3 border transition-all duration-300 ${
                            password && confirmPassword && password === confirmPassword && !isLoading
                              ? 'bg-white text-black border-transparent hover:bg-white/90 cursor-pointer'
                              : 'bg-[#111] text-white/50 border-white/10 cursor-not-allowed'
                          }`}
                        >
                          {isLoading ? 'Criando conta...' : 'Continuar'}
                        </motion.button>
                      </div>
                    </form>
                  </motion.div>
                ) : step === 'code' ? (
                  <motion.div
                    key="code-step"
                    initial={{ opacity: 0, x: 100 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 100 }}
                    transition={{ duration: 0.4, ease: 'easeOut' }}
                    className="space-y-6 text-center"
                  >
                    <div className="space-y-1">
                      <h1 className="text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white">
                        {mode === 'signup' ? 'Confirme seu email' : 'Código enviado'}
                      </h1>
                      <p className="text-[1.25rem] text-white/50 font-light">
                        {mode === 'signup' ? 'Digite o código enviado para ativar sua conta' : 'Verifique seu email'}
                      </p>
                    </div>

                    <div className="w-full">
                      <div className="relative rounded-full py-4 px-5 border border-white/10 bg-transparent">
                        <div className="flex items-center justify-center">
                          {code.map((digit, i) => (
                            <div key={i} className="flex items-center">
                              <div className="relative">
                                <input
                                  ref={(el) => {
                                    codeInputRefs.current[i] = el
                                  }}
                                  type="text"
                                  inputMode="numeric"
                                  pattern="[0-9]*"
                                  maxLength={1}
                                  value={digit}
                                  onChange={e => handleCodeChange(i, e.target.value)}
                                  onKeyDown={e => handleKeyDown(i, e)}
                                  disabled={isLoading}
                                  className="w-8 text-center text-xl bg-transparent text-white border-none focus:outline-none focus:ring-0 appearance-none disabled:opacity-50"
                                  style={{ caretColor: 'transparent' }}
                                />
                                {!digit && (
                                  <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center pointer-events-none">
                                    <span className="text-xl text-white">0</span>
                                  </div>
                                )}
                              </div>
                              {i < 5 && <span className="text-white/20 text-xl">|</span>}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex w-full gap-3">
                      <motion.button
                        onClick={handleBackClick}
                        className="rounded-full bg-white text-black font-medium px-8 py-3 hover:bg-white/90 transition-colors w-[30%]"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        transition={{ duration: 0.2 }}
                      >
                        Voltar
                      </motion.button>
                      <motion.button
                        disabled={isLoading}
                        className="flex-1 rounded-full font-medium py-3 border bg-[#111] text-white/50 border-white/10 cursor-not-allowed"
                      >
                        {isLoading ? 'Verificando...' : 'Digite o código'}
                      </motion.button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="success-step"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, ease: 'easeOut', delay: 0.3 }}
                    className="space-y-6 text-center"
                  >
                    <div className="space-y-1">
                      <h1 className="text-[2.5rem] font-bold leading-[1.1] tracking-tight text-white">
                        Sucesso!
                      </h1>
                      <p className="text-[1.25rem] text-white/50 font-light">
                        Redirecionando...
                      </p>
                    </div>

                    <motion.div
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                      className="py-10"
                    >
                      <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-white to-white/70 flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8 text-black"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}