'use client'

import { usePathname } from 'next/navigation'
import { SidebarProvider } from '@/contexts/SidebarContext'
import { ProfileProvider } from '@/contexts/ProfileContext'
import AppSidebar from '@/components/AppSidebar'
import Navbar from '@/components/Navbar'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

interface ConditionalLayoutProps {
  children: React.ReactNode
}

// Páginas que não devem mostrar sidebar e navbar (páginas públicas)
const paginasPublicas = ['/login', '/']

// Páginas que precisam de autenticação
const paginasProtegidas = ['/dashboard', '/analytics', '/chat', '/calendar', '/profile', '/perfil']

export default function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()
  
  // Verifica se é uma página pública
  const ehPaginaPublica = paginasPublicas.includes(pathname)
  
  // Verifica se é uma página protegida
  const ehPaginaProtegida = paginasProtegidas.some(rota => pathname.startsWith(rota))

  // Se é página pública, retorna apenas o conteúdo
  if (ehPaginaPublica) {
    return <>{children}</>
  }

  // Se é página protegida, aplica proteção e layout completo
  if (ehPaginaProtegida) {
    return (
      <ProtectedRoute>
        <ProfileProvider>
          <SidebarProvider>
            <div className="h-screen flex bg-background">
              {/* Sidebar */}
              <AppSidebar />
              
              {/* Conteúdo principal */}
              <div className="flex-1 flex flex-col overflow-hidden">
                {/* Navbar */}
                <Navbar />
                
                {/* Área de conteúdo */}
                <main className="flex-1 overflow-y-auto">
                  {children}
                </main>
              </div>
            </div>
          </SidebarProvider>
        </ProfileProvider>
      </ProtectedRoute>
    )
  }

  // Para outras páginas, aplica layout padrão sem proteção
  return (
    <ProfileProvider>
      <SidebarProvider>
        <div className="h-screen flex bg-background">
          {/* Sidebar */}
          <AppSidebar />
          
          {/* Conteúdo principal */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Navbar */}
            <Navbar />
            
            {/* Área de conteúdo */}
            <main className="flex-1 overflow-y-auto">
              {children}
            </main>
          </div>
        </div>
      </SidebarProvider>
    </ProfileProvider>
  )
}