"use client"

import * as React from "react"
import { FilterX } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Select, type SelectOption } from "@/components/ui/select"

// Opções para os selects
const tradeSimulatorOptions: SelectOption[] = [
  { value: "trade-simulator", label: "Trade Simulator" },
  { value: "live-trading", label: "Live Trading" },
  { value: "paper-trading", label: "Paper Trading" },
]

const directionOptions: SelectOption[] = [
  { value: "long", label: "Long" },
  { value: "short", label: "Short" },
  { value: "both", label: "Both" },
]

const positionOptions: SelectOption[] = [
  { value: "all-positions", label: "All positions" },
  { value: "open-positions", label: "Open positions" },
  { value: "closed-positions", label: "Closed positions" },
]

interface PositionsHeaderProps {
  className?: string
}

export default function PositionsHeader({ className }: PositionsHeaderProps) {
  const [tradeSimulator, setTradeSimulator] = React.useState("trade-simulator")
  const [direction, setDirection] = React.useState("long")
  const [positionFilter, setPositionFilter] = React.useState("all-positions")

  const handleClearFilter = () => {
    setTradeSimulator("trade-simulator")
    setDirection("long")
    setPositionFilter("all-positions")
  }

  return (
    <div className={`bg-background ${className}`}>
      <div className="px-6 py-3 ">
        <div className="flex items-center justify-between">
          <h2 className="text-xs font-medium text-foreground">
            Positions
          </h2>

          <div className="flex items-center gap-3">
            <Select
              options={tradeSimulatorOptions}
              value={tradeSimulator}
              onValueChange={setTradeSimulator}
              className="w-[140px]"
            />
            
            <Select
              options={directionOptions}
              value={direction}
              onValueChange={setDirection}
              className="w-[80px]"
            />
            
            <Select
              options={positionOptions}
              value={positionFilter}
              onValueChange={setPositionFilter}
              className="w-[120px]"
            />
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearFilter}
            className="text-muted-foreground hover:text-foreground h-8 px-3 text-xs gap-1"
          >
            <FilterX className="h-3 w-3" />
            Clear Filter
          </Button>
        </div>
      </div>
    </div>
  )
}
