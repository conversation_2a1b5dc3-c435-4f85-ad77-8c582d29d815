'use client'

import { useState } from 'react'
import {
  BookOpenIcon,
  Layers2Icon,
  LogOutIcon,
  PinIcon,
  SettingsIcon,
  UserIcon,
} from "lucide-react"
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { useProfile } from '@/hooks/useProfile'

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function UserMenu() {
  const { user, signOut, isLoading } = useAuth()
  const { profile } = useProfile()
  const router = useRouter()
  const [isSigningOut, setIsSigningOut] = useState(false)

  const handleLogout = async () => {
    try {
      setIsSigningOut(true)
      const result = await signOut()
      if (result.success) {
        router.push('/login')
      } else {
        console.error('Erro ao fazer logout:', result.error)
      }
    } catch (error) {
      console.error('Erro inesperado ao fazer logout:', error)
    } finally {
      setIsSigningOut(false)
    }
  }

  // Função para obter as iniciais do usuário
  const obterIniciais = (email: string) => {
    if (!email) return 'US'
    const partes = email.split('@')[0].split('.')
    if (partes.length >= 2) {
      return (partes[0][0] + partes[1][0]).toUpperCase()
    }
    return email.substring(0, 2).toUpperCase()
  }

  // Função para obter o nome de exibição
  const obterNomeExibicao = () => {
    if (profile?.full_name) {
      return profile.full_name
    }
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name
    }
    if (user?.user_metadata?.name) {
      return user.user_metadata.name
    }
    if (user?.email) {
      const nomeDoEmail = user.email.split('@')[0]
      return nomeDoEmail.charAt(0).toUpperCase() + nomeDoEmail.slice(1)
    }
    return 'Usuário'
  }

  // Função para navegar para páginas
  const navegarPara = (rota: string) => {
    router.push(rota)
  }

  // Se está carregando ou não há usuário, não renderiza
  if (isLoading || !user) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-auto p-2 hover:bg-accent focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-lg"
          disabled={isSigningOut}
        >
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={profile?.avatar_url || user.user_metadata?.avatar_url || user.user_metadata?.picture}
                alt="Imagem do perfil"
              />
              <AvatarFallback className="bg-primary/10 text-primary font-medium">
                {obterIniciais(user.email || 'US')}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col items-start min-w-0">
              <span className="text-sm font-medium text-foreground truncate max-w-[150px]">
                {obterNomeExibicao()}
              </span>
              <span className="text-xs text-muted-foreground truncate max-w-[150px]">
                {user.email}
              </span>
            </div>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64" align="end" sideOffset={8}>
        <DropdownMenuLabel className="flex min-w-0 flex-col p-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage
                src={profile?.avatar_url || user.user_metadata?.avatar_url || user.user_metadata?.picture}
                alt="Imagem do perfil"
              />
              <AvatarFallback className="bg-primary/10 text-primary font-medium">
                {obterIniciais(user.email || 'US')}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-foreground truncate text-sm font-medium">
                {obterNomeExibicao()}
              </p>
              <p className="text-muted-foreground truncate text-xs font-normal">
                {user.email}
              </p>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={() => navegarPara('/profile')}
            className="cursor-pointer"
          >
            <UserIcon size={16} className="opacity-60" aria-hidden="true" />
            <span>Meu Perfil</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => navegarPara('/configuracoes')}
            className="cursor-pointer"
          >
            <SettingsIcon size={16} className="opacity-60" aria-hidden="true" />
            <span>Configurações</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={() => navegarPara('/projetos')}
            className="cursor-pointer"
          >
            <Layers2Icon size={16} className="opacity-60" aria-hidden="true" />
            <span>Meus Projetos</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => navegarPara('/favoritos')}
            className="cursor-pointer"
          >
            <PinIcon size={16} className="opacity-60" aria-hidden="true" />
            <span>Favoritos</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={() => window.open('https://docs.supabase.com', '_blank')}
            className="cursor-pointer"
          >
            <BookOpenIcon size={16} className="opacity-60" aria-hidden="true" />
            <span>Documentação</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={handleLogout}
          className="cursor-pointer text-destructive focus:text-destructive"
          disabled={isSigningOut}
        >
          <LogOutIcon size={16} className="opacity-60" aria-hidden="true" />
          <span>{isSigningOut ? 'Saindo...' : 'Sair'}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}