'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/hooks/useAuth'

export interface UserProfile {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  updated_at?: string
}

interface ProfileContextType {
  profile: UserProfile | null
  isLoading: boolean
  error: string | null
  updateProfile: (updates: Partial<Pick<UserProfile, 'full_name' | 'avatar_url'>>) => Promise<{ success: boolean; data?: UserProfile; error?: string }>
  uploadAvatar: (file: File) => Promise<{ success: boolean; url?: string; error?: string }>
  removeAvatar: () => Promise<{ success: boolean; error?: string } | undefined>
  clearError: () => void
  refreshProfile: () => Promise<void>
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined)

export const ProfileProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Cache para evitar múltiplas requisições
  const [isCacheValid, setIsCacheValid] = useState(false)

  // Carregar perfil do usuário
  const loadProfile = useCallback(async (forceRefresh = false) => {
    if (!user) {
      setProfile(null)
      setIsLoading(false)
      setIsCacheValid(false)
      return
    }

    // Verificar cache - só recarrega se forçado ou cache inválido
    if (!forceRefresh && profile && isCacheValid) {
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Verificar se existe um perfil na tabela profiles
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError && profileError.code !== 'PGRST116') {
        throw profileError
      }

      // Se não existe perfil, criar um
      if (!profileData) {
        const newProfile: Omit<UserProfile, 'updated_at'> = {
          id: user.id,
          email: user.email || '',
          full_name: user.user_metadata?.full_name || '',
          avatar_url: user.user_metadata?.avatar_url || '',
          created_at: user.created_at
        }

        const { data: insertedProfile, error: insertError } = await supabase
          .from('profiles')
          .insert([{ ...newProfile, updated_at: new Date().toISOString() }])
          .select()
          .single()

        if (insertError) throw insertError
        
        setProfile(insertedProfile)
      } else {
        setProfile(profileData)
      }

      setIsCacheValid(true)
    } catch (error: unknown) {
      console.error('Erro ao carregar perfil:', error)
      setError('Erro ao carregar perfil do usuário')
      setIsCacheValid(false)
    } finally {
      setIsLoading(false)
    }
  }, [user, isCacheValid, profile])

  // Função para forçar refresh
  const refreshProfile = async () => {
    await loadProfile(true)
  }

  // Atualizar perfil
  const updateProfile = async (updates: Partial<Pick<UserProfile, 'full_name' | 'avatar_url'>>) => {
    if (!user || !profile) {
      throw new Error('Usuário não autenticado ou perfil não carregado')
    }

    try {
      setError(null)

      const updatedData = {
        ...updates,
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('profiles')
        .update(updatedData)
        .eq('id', user.id)
        .select()
        .single()

      if (error) throw error

      setProfile(data)
      // Cache continua válido após atualização
      return { success: true, data }
    } catch (error: unknown) {
      console.error('Erro ao atualizar perfil:', error)
      const errorMessage = 'Erro ao salvar alterações'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  // Upload de avatar
  const uploadAvatar = async (file: File) => {
    if (!user) {
      throw new Error('Usuário não autenticado')
    }

    try {
      setError(null)

      // Validar arquivo
      if (!file.type.startsWith('image/')) {
        throw new Error('Por favor, selecione apenas arquivos de imagem')
      }

      if (file.size > 5 * 1024 * 1024) {
        throw new Error('A imagem deve ter no máximo 5MB')
      }

      // Gerar nome único para o arquivo
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}-${Date.now()}.${fileExt}`
      const filePath = `avatars/${fileName}`

      // Remover avatar anterior se existir
      if (profile?.avatar_url) {
        const oldPath = profile.avatar_url.split('/').pop()
        if (oldPath) {
          await supabase.storage
            .from('avatars')
            .remove([`avatars/${oldPath}`])
        }
      }

      // Upload do novo arquivo
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file)

      if (uploadError) throw uploadError

      // Obter URL pública
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath)

      // Atualizar perfil com nova URL
      const result = await updateProfile({ avatar_url: publicUrl })
      
      if (result.success) {
        return { success: true, url: publicUrl }
      } else {
        throw new Error(result.error)
      }
    } catch (error: unknown) {
      console.error('Erro ao fazer upload do avatar:', error)
      const errorMessage = error instanceof Error ? error.message : 'Erro ao fazer upload da imagem'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  // Remover avatar
  const removeAvatar = async () => {
    if (!user || !profile?.avatar_url) return

    try {
      setError(null)

      // Remover arquivo do storage
      const fileName = profile.avatar_url.split('/').pop()
      if (fileName) {
        await supabase.storage
          .from('avatars')
          .remove([`avatars/${fileName}`])
      }

      // Atualizar perfil
      return await updateProfile({ avatar_url: '' })
    } catch (error: unknown) {
      console.error('Erro ao remover avatar:', error)
      const errorMessage = 'Erro ao remover avatar'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    }
  }

  // Carregar perfil quando o usuário mudar
  useEffect(() => {
    if (user) {
      // Invalidar cache quando usuário muda
      setIsCacheValid(false)

      // Carregar perfil diretamente no useEffect
      const loadUserProfile = async () => {
        try {
          setIsLoading(true)
          setError(null)

          // Verificar se existe um perfil na tabela profiles
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single()

          if (profileError && profileError.code !== 'PGRST116') {
            throw profileError
          }

          // Se não existe perfil, criar um
          if (!profileData) {
            const newProfile: Omit<UserProfile, 'updated_at'> = {
              id: user.id,
              email: user.email || '',
              full_name: user.user_metadata?.full_name || '',
              avatar_url: user.user_metadata?.avatar_url || '',
              created_at: user.created_at
            }

            const { data: insertedProfile, error: insertError } = await supabase
              .from('profiles')
              .insert([{ ...newProfile, updated_at: new Date().toISOString() }])
              .select()
              .single()

            if (insertError) throw insertError

            setProfile(insertedProfile)
          } else {
            setProfile(profileData)
          }

          setIsCacheValid(true)
        } catch (error: unknown) {
          console.error('Erro ao carregar perfil:', error)
          setError('Erro ao carregar perfil do usuário')
          setIsCacheValid(false)
        } finally {
          setIsLoading(false)
        }
      }

      loadUserProfile()
    } else {
      setProfile(null)
      setIsLoading(false)
      setIsCacheValid(false)
    }
  }, [user?.id, user]) // Incluir user como dependência

  const value: ProfileContextType = {
    profile,
    isLoading,
    error,
    updateProfile,
    uploadAvatar,
    removeAvatar,
    clearError: () => setError(null),
    refreshProfile
  }

  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  )
}

export const useProfile = (): ProfileContextType => {
  const context = useContext(ProfileContext)
  if (context === undefined) {
    throw new Error('useProfile deve ser usado dentro de um ProfileProvider')
  }
  return context
}