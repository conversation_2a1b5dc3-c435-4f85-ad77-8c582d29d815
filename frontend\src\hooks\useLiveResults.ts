'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  LiveResultsResponse,
  BetExplorerResponse,
  DashboardMatch,
  LiveResultsHookResult
} from '@/lib/betexplorer-types'

/**
 * Hook para consumir dados de eventos ao vivo do BetExplorer
 * Inclui cache, revalidação automática e transformação de dados
 */

interface UseLiveResultsOptions {
  autoRefresh?: boolean
  refreshInterval?: number // em milissegundos
  eventId?: string // Filtrar por evento específico
}

export function useLiveResults(options: UseLiveResultsOptions = {}): LiveResultsHookResult {
  const {
    autoRefresh = true,
    refreshInterval = 5000, // 30 segundos por padrão
    eventId
  } = options

  // Estados
  const [data, setData] = useState<LiveResultsResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [footballEvents, setFootballEvents] = useState<DashboardMatch[]>([])

  // Refs para controle
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Função para buscar dados
  const fetchLiveResults = useCallback(async () => {
    try {
      // Cancelar requisição anterior se existir
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Criar novo AbortController
      abortControllerRef.current = new AbortController()

      // Construir URL da API
      let apiUrl = '/api/betexplorer/live-results'
      if (eventId) {
        apiUrl += `?eventId=${eventId}`
      }

      console.log('🔄 Buscando eventos ao vivo...')

      const response = await fetch(apiUrl, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`)
      }

      const result: BetExplorerResponse<LiveResultsResponse> = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Erro desconhecido na API')
      }

      if (!result.data) {
        throw new Error('Dados não encontrados na resposta')
      }

      // Atualizar estados
      setData(result.data)
      setError(null)
      setLastUpdated(new Date())

      console.log(`✅ Eventos carregados: ${Object.keys(result.data.events).length} jogos de futebol`)

    } catch (err) {
      // Ignorar erros de abort (cancelamento)
      if (err instanceof Error && err.name === 'AbortError') {
        return
      }

      console.error('❌ Erro ao buscar eventos ao vivo:', err)
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setLoading(false)
    }
  }, [eventId])

  // Função para transformar dados em DashboardMatch
  const transformToFootballEvents = useCallback((liveData: LiveResultsResponse): DashboardMatch[] => {
    if (!liveData.events) return []

    return Object.entries(liveData.events).map(([eventId, event]) => {
      // Buscar odds para este evento
      const eventOdds = {
        live_odds_1x2: liveData.odds.live_odds_1x2?.[eventId] || undefined,
        live_odds_ou: liveData.odds.live_odds_ou?.[eventId] || undefined,
        live_odds_dc: liveData.odds.live_odds_dc?.[eventId] || undefined,
        live_odds_dnb: liveData.odds.live_odds_dnb?.[eventId] || undefined,
        live_odds_btts: liveData.odds.live_odds_btts?.[eventId] || undefined,
        live_odds_ah: liveData.odds.live_odds_ah?.[eventId] || undefined
      }

      // Buscar informações do estágio do evento
      const stageInfo = liveData.event_stages[event.event_stage_id]
      const stageName = stageInfo ? stageInfo[0] : 'Competição'

      return {
        event_id: eventId,
        homeTeam: 'Time Casa', // Será preenchido pelo useMatchDetails
        awayTeam: 'Time Visitante', // Será preenchido pelo useMatchDetails
        score: event.score,
        minute: event.minute,
        finished: event.finished === 1,
        competition: stageName,
        country: 'País', // Será preenchido pelo useMatchDetails
        odds: eventOdds,
        isLive: event.finished === 0 && event.minute > 0
      } as DashboardMatch
    })
  }, [])

  // Efeito para transformar dados quando mudarem
  useEffect(() => {
    if (data) {
      const events = transformToFootballEvents(data)
      setFootballEvents(events)
    }
  }, [data, transformToFootballEvents])

  // Efeito para buscar dados iniciais
  useEffect(() => {
    fetchLiveResults()
  }, [fetchLiveResults])

  // Efeito para auto-refresh
  useEffect(() => {
    if (!autoRefresh) return

    // Limpar intervalo anterior
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    // Configurar novo intervalo
    intervalRef.current = setInterval(() => {
      if (!loading) { // Só atualizar se não estiver carregando
        fetchLiveResults()
      }
    }, refreshInterval)

    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [autoRefresh, refreshInterval, loading, fetchLiveResults])

  // Cleanup geral
  useEffect(() => {
    return () => {
      // Cancelar requisição em andamento
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      
      // Limpar intervalo
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // Função para refetch manual
  const refetch = useCallback(async () => {
    setLoading(true)
    await fetchLiveResults()
  }, [fetchLiveResults])

  return {
    data,
    loading,
    error,
    refetch,
    footballEvents,
    lastUpdated
  }
}

// Hook simplificado para casos básicos (com auto-refresh desabilitado para evitar loops)
export function useFootballEvents() {
  const { footballEvents, loading, error, refetch, lastUpdated } = useLiveResults({
    autoRefresh: false, // Desabilitado para evitar loops
    refreshInterval: 60000 // Intervalo maior se habilitado
  })

  return {
    events: footballEvents,
    loading,
    error,
    refetch,
    lastUpdated,
    eventsCount: footballEvents.length
  }
}
