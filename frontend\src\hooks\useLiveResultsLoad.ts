'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  DashboardMatch,
  BetExplorerResponse 
} from '@/lib/betexplorer-types'

/**
 * Hook otimizado que carrega live results + detalhes em uma única chamada
 * Evita loops infinitos e múltiplas requisições
 */

interface LiveResultsLoadResponse {
  matches: DashboardMatch[]
  totalMatches: number
  lastUpdated: string
  footballOnly: boolean
}

interface UseLiveResultsLoadOptions {
  maxMatches?: number
  autoRefresh?: boolean
  refreshInterval?: number // em milissegundos
}

interface UseLiveResultsLoadResult {
  matches: DashboardMatch[]
  loading: boolean
  error: string | null
  lastUpdated: Date | null
  totalMatches: number
  refetch: () => Promise<void>
}

export function useLiveResultsLoad(options: UseLiveResultsLoadOptions = {}): UseLiveResultsLoadResult {
  const {
    maxMatches = 4,
    autoRefresh = false, // Desabilitado por padrão para evitar loops
    refreshInterval = 120000 // 2 minutos por padrão (mais conservador)
  } = options

  // Estados
  const [matches, setMatches] = useState<DashboardMatch[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [totalMatches, setTotalMatches] = useState(0)

  // Refs para controle
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const isLoadingRef = useRef(false)

  // Função para buscar dados (sem dependências para evitar loops)
  const fetchData = useCallback(async (forceRefresh = false) => {
    // Evitar múltiplas requisições simultâneas
    if (isLoadingRef.current) {
      console.log('⏳ Requisição já em andamento, ignorando...')
      return
    }

    try {
      isLoadingRef.current = true
      setLoading(true)
      setError(null)

      // Cancelar requisição anterior se existir
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Criar novo AbortController
      abortControllerRef.current = new AbortController()

      // Construir URL usando valores atuais (não dependências)
      const params = new URLSearchParams({
        maxMatches: maxMatches.toString()
      })

      if (forceRefresh) {
        params.append('refresh', 'true')
      }

      const apiUrl = `/api/betexplorer/live_results_load?${params.toString()}`

      console.log('🔄 Buscando dados combinados...', { maxMatches, forceRefresh })

      const response = await fetch(apiUrl, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`)
      }

      const result: BetExplorerResponse<LiveResultsLoadResponse> = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Erro desconhecido na API')
      }

      if (!result.data) {
        throw new Error('Dados não encontrados na resposta')
      }

      // Atualizar estados
      setMatches(result.data.matches)
      setTotalMatches(result.data.totalMatches)
      setLastUpdated(new Date(result.data.lastUpdated))
      setError(null)

      console.log(`✅ Dados carregados: ${result.data.matches.length} jogos`, {
        cached: (result as { cached?: boolean }).cached,
        totalMatches: result.data.totalMatches
      })

    } catch (err) {
      // Ignorar erros de abort (cancelamento)
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('🚫 Requisição cancelada')
        return
      }

      console.error('❌ Erro ao buscar dados combinados:', err)
      setError(err instanceof Error ? err.message : 'Erro desconhecido')
    } finally {
      setLoading(false)
      isLoadingRef.current = false
    }
  }, [maxMatches]) // Incluir maxMatches como dependência

  // Função para refetch manual
  const refetch = useCallback(async () => {
    await fetchData(true) // Force refresh
  }, [fetchData])

  // Efeito para buscar dados iniciais (apenas uma vez)
  useEffect(() => {
    console.log('🚀 Iniciando busca inicial de dados...')
    fetchData()
  }, [fetchData]) // Incluir fetchData como dependência

  // Efeito para auto-refresh (opcional e conservador)
  useEffect(() => {
    if (!autoRefresh) {
      console.log('🔄 Auto-refresh desabilitado')
      return
    }

    // Limpar intervalo anterior
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    console.log(`⏰ Configurando auto-refresh a cada ${refreshInterval / 1000}s`)

    // Configurar novo intervalo
    intervalRef.current = setInterval(() => {
      if (!isLoadingRef.current) {
        console.log('🔄 Auto-refresh executando...')
        fetchData()
      } else {
        console.log('⏳ Auto-refresh pulado - requisição em andamento')
      }
    }, refreshInterval)

    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        console.log('🛑 Auto-refresh limpo')
      }
    }
  }, [autoRefresh, refreshInterval, fetchData]) // Incluir fetchData como dependência

  // Cleanup geral
  useEffect(() => {
    return () => {
      // Cancelar requisição em andamento
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      
      // Limpar intervalo
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      
      // Reset loading flag
      isLoadingRef.current = false
    }
  }, [])

  return {
    matches,
    loading,
    error,
    lastUpdated,
    totalMatches,
    refetch
  }
}

// Hook simplificado para o dashboard
export function useDashboardMatches() {
  return useLiveResultsLoad({
    maxMatches: 4,
    autoRefresh: false, // Refresh manual apenas
    refreshInterval: 120000 // 2 minutos se habilitado
  })
}
