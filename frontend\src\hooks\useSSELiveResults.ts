'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { DashboardMatch } from '@/lib/betexplorer-types'

/**
 * Hook para receber dados em tempo real via Server-Sent Events
 * Elimina loops infinitos usando conexão persistente
 */

export interface SSELiveResultsData {
  matches: DashboardMatch[]
  totalMatches: number
  lastUpdated: string
  footballOnly: boolean
}

interface UseSSELiveResultsOptions {
  maxMatches?: number
  autoReconnect?: boolean
  reconnectDelay?: number
}

interface UseSSELiveResultsResult {
  matches: DashboardMatch[]
  loading: boolean
  error: string | null
  lastUpdated: Date | null
  totalMatches: number
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  reconnect: () => void
}

export function useSSELiveResults(options: UseSSELiveResultsOptions = {}): UseSSELiveResultsResult {
  const {
    maxMatches = 4,
    autoReconnect = true,
    reconnectDelay = 5000
  } = options

  // Estados
  const [matches, setMatches] = useState<DashboardMatch[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [totalMatches, setTotalMatches] = useState(0)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('connecting')

  // Refs
  const eventSourceRef = useRef<EventSource | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isUnmountedRef = useRef(false)

  // Função para conectar ao SSE
  const connect = useCallback(() => {
    if (isUnmountedRef.current) return

    // Fechar conexão anterior se existir
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
    }

    console.log('🔌 Conectando ao SSE...', { maxMatches })
    setConnectionStatus('connecting')
    setError(null)

    // Criar nova conexão EventSource
    const url = `/api/betexplorer/live-stream?maxMatches=${maxMatches}`
    console.log('🌐 URL do SSE:', url)

    let eventSource: EventSource

    try {
      eventSource = new EventSource(url)
      eventSourceRef.current = eventSource
      console.log('✅ EventSource criado com sucesso')
    } catch (error) {
      console.error('❌ Erro ao criar EventSource:', error)
      setError('Erro ao criar conexão SSE')
      return
    }

    // Handler principal para mensagens (evento padrão)
    eventSource.onmessage = (event) => {
      if (isUnmountedRef.current) return

      console.log('📨 Mensagem SSE recebida:', event.data)

      try {
        const data = JSON.parse(event.data)
        console.log('📨 Dados processados:', data)

        // Verificar se é dados válidos
        if (data.matches && Array.isArray(data.matches)) {
          setMatches(data.matches)
          setTotalMatches(data.totalMatches || data.matches.length)
          setLastUpdated(new Date(data.lastUpdated || new Date().toISOString()))
          setLoading(false)
          setConnectionStatus('connected')
          setError(null)

          console.log('✅ Dados atualizados no estado:', data.matches.length, 'jogos')
        } else {
          console.log('⚠️ Dados recebidos não são válidos:', data)
        }
      } catch (err) {
        console.error('❌ Erro ao processar mensagem SSE:', err, event.data)
        setError('Erro ao processar dados')
      }
    }

    // Handler para erros
    eventSource.addEventListener('error', (event) => {
      if (isUnmountedRef.current) return
      
      try {
        const errorData = JSON.parse((event as unknown as { data: string }).data)
        console.error('❌ Erro recebido via SSE:', errorData.error)
        setError(errorData.error)
      } catch {
        console.error('❌ Erro de conexão SSE')
        setError('Erro de conexão')
      }
      
      setConnectionStatus('error')
    })

    // Handler para heartbeat (manter conexão viva)
    eventSource.addEventListener('heartbeat', () => {
      if (isUnmountedRef.current) return
      console.log('💓 Heartbeat recebido')
    })

    // Handler para quando conexão abre
    eventSource.onopen = () => {
      if (isUnmountedRef.current) return
      console.log('✅ Conexão SSE estabelecida')
      setConnectionStatus('connected')
      setError(null)
    }

    // Handler para erros de conexão
    eventSource.onerror = (event) => {
      if (isUnmountedRef.current) return

      console.error('❌ Erro na conexão SSE:', event)
      console.log('📊 Estado da conexão:', eventSource.readyState)
      setConnectionStatus('error')
      setLoading(false)

      // Auto-reconectar se habilitado
      if (autoReconnect && !isUnmountedRef.current) {
        console.log(`🔄 Tentando reconectar em ${reconnectDelay / 1000}s...`)
        reconnectTimeoutRef.current = setTimeout(() => {
          if (!isUnmountedRef.current) {
            connect()
          }
        }, reconnectDelay)
      }
    }



  }, [maxMatches, autoReconnect, reconnectDelay])

  // Função para reconectar manualmente
  const reconnect = useCallback(() => {
    console.log('🔄 Reconectando manualmente...')
    
    // Limpar timeout de reconexão se existir
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    
    connect()
  }, [connect])

  // Efeito para conectar inicialmente
  useEffect(() => {
    connect()
  }, [connect])

  // Cleanup
  useEffect(() => {
    return () => {
      isUnmountedRef.current = true
      
      // Fechar conexão SSE
      if (eventSourceRef.current) {
        console.log('🛑 Fechando conexão SSE')
        eventSourceRef.current.close()
        eventSourceRef.current = null
      }
      
      // Limpar timeout de reconexão
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
        reconnectTimeoutRef.current = null
      }
    }
  }, [])

  return {
    matches,
    loading,
    error,
    lastUpdated,
    totalMatches,
    connectionStatus,
    reconnect
  }
}

// Hook simplificado para o dashboard
export function useDashboardSSE() {
  return useSSELiveResults({
    maxMatches: 4,
    autoReconnect: true,
    reconnectDelay: 5000
  })
}
