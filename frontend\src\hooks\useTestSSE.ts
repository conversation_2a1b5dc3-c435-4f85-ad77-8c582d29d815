'use client'

import { useState, useEffect, useRef } from 'react'

/**
 * Hook de teste para SSE
 */

export function useTestSSE() {
  const [messages, setMessages] = useState<string[]>([])
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('connecting')
  const eventSourceRef = useRef<EventSource | null>(null)

  useEffect(() => {
    console.log('🧪 Iniciando teste SSE...')
    
    const eventSource = new EventSource('/api/test-sse')
    eventSourceRef.current = eventSource

    eventSource.onopen = () => {
      console.log('✅ Conexão de teste SSE estabelecida')
      setConnectionStatus('connected')
    }

    eventSource.onmessage = (event) => {
      console.log('📨 Mensagem de teste recebida:', event.data)
      try {
        const data = JSON.parse(event.data)
        setMessages(prev => [...prev, `${data.timestamp}: ${data.message}`])
      } catch (error) {
        console.error('❌ Erro ao processar mensagem:', error)
      }
    }

    eventSource.onerror = (event) => {
      console.error('❌ Erro na conexão de teste SSE:', event)
      setConnectionStatus('error')
    }

    // Cleanup
    return () => {
      console.log('🛑 Fechando conexão de teste SSE')
      eventSource.close()
    }
  }, [])

  return {
    messages,
    connectionStatus
  }
}
