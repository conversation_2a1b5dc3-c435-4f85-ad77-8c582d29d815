'use client';

import { useEffect, useState } from 'react';

type Theme = 'dark' | 'light';

export function useTheme() {
  const [theme, setTheme] = useState<Theme>('dark'); // Padrão escuro
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Verificar se há um tema salvo no localStorage
    const savedTheme = localStorage.getItem('theme') as Theme;
    if (savedTheme && (savedTheme === 'dark' || savedTheme === 'light')) {
      setTheme(savedTheme);
      document.documentElement.className = savedTheme;
    } else {
      // Se não há tema salvo, usar escuro como padrão
      document.documentElement.className = 'dark';
      localStorage.setItem('theme', 'dark');
    }
    setIsInitialized(true);
  }, []);

  const toggleTheme = () => {
    if (!isInitialized) return;
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
    document.documentElement.className = newTheme;
    localStorage.setItem('theme', newTheme);
  };

  const setSpecificTheme = (newTheme: Theme) => {
    if (!isInitialized) return;
    setTheme(newTheme);
    document.documentElement.className = newTheme;
    localStorage.setItem('theme', newTheme);
  };

  return {
    theme,
    toggleTheme,
    setTheme: setSpecificTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
    isInitialized,
  };
}
