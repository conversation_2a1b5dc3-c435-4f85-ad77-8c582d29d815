/**
 * Tipos TypeScript para as APIs do BetExplorer
 * Baseado na documentação completa das APIs descobertas
 */

// ============================================================================
// TIPOS BASE
// ============================================================================

export interface BetExplorerResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}

// ============================================================================
// LIVE RESULTS API
// ============================================================================

export interface LiveEvent {
  event_id: string
  score: string
  minute: number
  finished: 0 | 1  // 0 = ao vivo, 1 = finalizado
  sport_id: number // 1 = futebol, 2 = tênis, etc.
  event_stage_id: string
}

export interface EventStage {
  stage_id: string
  name: string
  abbreviation: string
}

export interface LiveOdds {
  live_odds_1x2?: Record<string, number[]>     // Odds 1X2
  live_odds_ou?: Record<string, number[]>      // Odds Over/Under
  live_odds_dc?: Record<string, number[]>      // Odds Double Chance
  live_odds_dnb?: Record<string, number[]>     // Odds Draw No Bet
  live_odds_btts?: Record<string, number[]>    // Odds Both Teams To Score
  live_odds_ah?: Record<string, number[]>      // Odds Asian Handicap
}

export interface LiveResultsResponse {
  events: Record<string, LiveEvent>
  event_stages: Record<string, [string, string]> // [nome, abreviação]
  odds: LiveOdds
  bookmakers: string[]
}

// ============================================================================
// MATCH CONTENT API
// ============================================================================

export interface MatchDetails {
  event_id: string
  homeTeam: string
  awayTeam: string
  homeTeamLogo?: string
  awayTeamLogo?: string
  tournament: string
  country: string
  recentResults?: RecentResult[]
  headToHead?: HeadToHeadData
}

export interface RecentResult {
  date: string
  homeTeam: string
  awayTeam: string
  score: string
  competition: string
}

export interface HeadToHeadData {
  totalGames: number
  homeWins: number
  draws: number
  awayWins: number
  recentMatches: RecentResult[]
}

// ============================================================================
// STANDINGS API
// ============================================================================

export interface StandingsEntry {
  position: number
  team: string
  teamId?: string
  games: number
  wins: number
  draws: number
  losses: number
  goalsFor: number
  goalsAgainst: number
  goalDifference: number
  points: number
}

export interface FormEntry {
  position: number
  team: string
  teamId?: string
  recentForm: string // Ex: "WDLWW"
  recentPoints: number
  formGames: number
}

export interface OverUnderEntry {
  team: string
  teamId?: string
  over25: number
  under25: number
  over15: number
  under15: number
  over25Percentage: number
  under25Percentage: number
  totalGames: number
}

export interface HalfTimeFullTimeEntry {
  team: string
  teamId?: string
  htWins: number
  htDraws: number
  htLosses: number
  ftWins: number
  ftDraws: number
  ftLosses: number
  totalGames: number
}

export interface TopScorer {
  position: number
  player: string
  team: string
  goals: number
  games: number
  goalsPerGame: number
}

export type StandingsType = 'table' | 'form' | 'over_under' | 'ht_ft' | 'top_scorers'
export type StandingsSubType = 'overall' | 'home' | 'away'

export interface StandingsResponse {
  type: StandingsType
  subType: StandingsSubType
  tournament: string
  season: string
  data: StandingsEntry[] | FormEntry[] | OverUnderEntry[] | HalfTimeFullTimeEntry[] | TopScorer[]
}

// ============================================================================
// TEAM MATCHES API
// ============================================================================

export interface TeamMatch {
  date: string
  homeTeam: string
  awayTeam: string
  score: string
  competition: string
  isHome: boolean
  result: 'W' | 'D' | 'L' // Win, Draw, Loss
}

export interface TeamMatchesResponse {
  teamId: string
  teamName: string
  type: 0 | 1 | 2 // 0 = todos, 1 = casa, 2 = fora
  period: 3 | 6 | 9 // meses
  matches: TeamMatch[]
  summary: {
    totalGames: number
    wins: number
    draws: number
    losses: number
    goalsFor: number
    goalsAgainst: number
  }
}

// ============================================================================
// MUTUAL MATCHES API
// ============================================================================

export interface MutualMatch {
  date: string
  homeTeam: string
  awayTeam: string
  score: string
  competition: string
  season?: string
}

export interface MutualMatchesResponse {
  team1: string
  team2: string
  totalMatches: number
  team1Wins: number
  draws: number
  team2Wins: number
  matches: MutualMatch[]
  summary: {
    team1WinPercentage: number
    drawPercentage: number
    team2WinPercentage: number
    averageGoalsPerGame: number
  }
}

// ============================================================================
// NEXT RESULTS API
// ============================================================================

export interface NextEvent {
  event_id: string
  homeTeam: string
  awayTeam: string
  competition: string
  date: string
  time: string
  odds?: {
    home: number
    draw: number
    away: number
  }
}

export interface NextResultsResponse {
  events: NextEvent[]
  totalEvents: number
}

// ============================================================================
// TIPOS COMBINADOS PARA COMPONENTES
// ============================================================================

export interface DashboardMatch {
  event_id: string
  homeTeam: string
  awayTeam: string
  homeTeamLogo?: string
  awayTeamLogo?: string
  score: string
  minute: number
  finished: boolean
  competition: string
  country: string
  odds: LiveOdds
  isLive: boolean
}

export interface MatchStatistics {
  matchDetails: MatchDetails
  standings?: StandingsResponse
  mutualMatches?: MutualMatchesResponse
  homeTeamHistory?: TeamMatchesResponse
  awayTeamHistory?: TeamMatchesResponse
}

// ============================================================================
// TIPOS PARA HOOKS
// ============================================================================

export interface UseApiResult<T> {
  data: T | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export interface LiveResultsHookResult extends UseApiResult<LiveResultsResponse> {
  footballEvents: DashboardMatch[]
  lastUpdated: Date | null
}

export interface MatchDetailsHookResult extends UseApiResult<MatchDetails> {
  eventId: string | null
}

// ============================================================================
// CONFIGURAÇÕES E CONSTANTES
// ============================================================================

export const BETEXPLORER_CONFIG = {
  BASE_URL: 'https://www.betexplorer.com',
  RATE_LIMIT_MS: 2000, // 2 segundos entre requisições
  CACHE_TTL_MS: 30000, // 30 segundos de cache para dados ao vivo
  HEADERS: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/html, */*',
    'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
    'X-Requested-With': 'XMLHttpRequest',
    'Referer': 'https://www.betexplorer.com'
  }
} as const

export const SPORT_IDS = {
  FOOTBALL: 1,
  TENNIS: 2,
  BASKETBALL: 3,
  HOCKEY: 4
} as const

export const MARKET_TYPES = {
  ODDS_1X2: '1x2',
  OVER_UNDER: 'ou',
  ASIAN_HANDICAP: 'ah',
  DRAW_NO_BET: 'dnb',
  DOUBLE_CHANCE: 'dc',
  BOTH_TEAMS_TO_SCORE: 'btts'
} as const
